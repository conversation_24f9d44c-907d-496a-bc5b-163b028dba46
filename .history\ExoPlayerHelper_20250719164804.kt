package com.myplayer

import android.content.Context
import android.net.Uri
import android.util.Log
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.FrameLayout
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView

object ExoPlayerHelper {

    private var exoPlayer: ExoPlayer? = null
    private var webView: WebView? = null

    fun initializePlayer(
        context: Context,
        playerView: PlayerView,
        webViewContainer: FrameLayout,
        url: String,
        userAgent: String,
        referer: String?,
        onPlayerError: (String) -> Unit
    ) {
        releasePlayer() // Release any existing player instances

        exoPlayer = ExoPlayer.Builder(context).build().apply {
            playerView.player = this
            val httpDataSourceFactory = DefaultHttpDataSource.Factory()
                .setUserAgent(userAgent)
                .setDefaultRequestProperties(
                    referer?.let { mapOf("Referer" to it) } ?: emptyMap()
                )

            val mediaItem = MediaItem.Builder()
                .setUri(Uri.parse(url))
                .build()

            setMediaItem(mediaItem)
            prepare()
            playWhenReady = true

            addListener(object : Player.Listener {
                override fun onPlayerError(error: PlaybackException) {
                    super.onPlayerError(error)
                    Log.e("ExoPlayerHelper", "ExoPlayer error: ${error.message}", error)
                    onPlayerError(error.message ?: "Unknown ExoPlayer error")
                }
            })
        }
    }

    fun initializeWebViewPlayer(
        context: Context,
        webViewContainer: FrameLayout,
        url: String
    ) {
        releasePlayer() // Release ExoPlayer if it's active

        webView = WebView(context).apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
            settings.javaScriptEnabled = true
            settings.domStorageEnabled = true
            webViewClient = WebViewClient() // Basic WebViewClient
            loadUrl(url)
        }
        webViewContainer.addView(webView)
    }

    fun releasePlayer() {
        exoPlayer?.release()
        exoPlayer = null
        webView?.apply {
            stopLoading()
            clearHistory()
            loadUrl("about:blank")
            removeAllViews()
            destroy()
        }
        webView = null
    }
}
