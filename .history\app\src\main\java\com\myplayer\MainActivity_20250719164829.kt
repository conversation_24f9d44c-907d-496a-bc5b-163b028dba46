package com.myplayer

import android.content.Intent
import android.os.Bundle
import android.widget.FrameLayout
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewmodel.compose.viewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.media3.ui.PlayerView
import com.myplayer.ui.theme.MyPlayerTheme
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

data class PlayerUiState(
    val url: String = "",
    val userAgent: String = "ShamPlayer/1.0",
    val referer: String? = null,
    val playerType: String = "ExoPlayer",
    val presets: List<Preset> = emptyList(),
    val errorMessage: String = ""
)

class MainViewModel(applicationContext: android.content.Context) : ViewModel() {

    private val _uiState = MutableStateFlow(PlayerUiState())
    val uiState: StateFlow<PlayerUiState> = _uiState.asStateFlow()

    private val presetManager = PresetManager(applicationContext)
    private var exoPlayerView: PlayerView? = null
    private var webViewContainer: FrameLayout? = null
    private val context = applicationContext

    init {
        loadPresets()
    }

    fun updateUrl(newUrl: String) {
        _uiState.update { it.copy(url = newUrl, errorMessage = "") }
    }

    fun updateUserAgent(newUserAgent: String) {
        _uiState.update { it.copy(userAgent = newUserAgent, errorMessage = "") }
    }

    fun updateReferer(newReferer: String) {
        _uiState.update { it.copy(referer = newReferer.ifEmpty { null }, errorMessage = "") }
    }

    fun updatePlayerType(newType: String) {
        _uiState.update { it.copy(playerType = newType, errorMessage = "") }
        ExoPlayerHelper.releasePlayer() // Release player when switching type
    }

    fun setExoPlayerView(playerView: PlayerView, container: FrameLayout) {
        exoPlayerView = playerView
        webViewContainer = container
    }

    fun setWebViewContainer(container: FrameLayout) {
        webViewContainer = container
        exoPlayerView = null // Ensure ExoPlayerView is null if using WebView
    }

    fun playStream() {
        val currentUrl = _uiState.value.url
        val currentUserAgent = _uiState.value.userAgent
        val currentReferer = _uiState.value.referer
        val currentPlayerType = _uiState.value.playerType

        if (currentUrl.isEmpty()) {
            _uiState.update { it.copy(errorMessage = "URL cannot be empty.") }
            return
        }

        _uiState.update { it.copy(errorMessage = "") } // Clear previous errors

        ExoPlayerHelper.releasePlayer() // Always release before playing new stream

        if (currentPlayerType == "ExoPlayer") {
            exoPlayerView?.let { playerView ->
                webViewContainer?.let { container ->
                    ExoPlayerHelper.initializePlayer(
                        context,
                        playerView,
                        container,
                        currentUrl,
                        currentUserAgent,
                        currentReferer
                    ) { errorMessage ->
                        _uiState.update { it.copy(errorMessage = "ExoPlayer failed: $errorMessage. Falling back to Web Player.") }
                        // Fallback to WebView if ExoPlayer fails
                        initializeWebViewFallback(currentUrl)
                    }
                }
            }
        } else {
            initializeWebViewFallback(currentUrl)
        }
    }

    private fun initializeWebViewFallback(url: String) {
        webViewContainer?.let { container ->
            ExoPlayerHelper.initializeWebViewPlayer(context, container, url)
        }
    }

    fun savePreset() {
        val currentUrl = _uiState.value.url
        val currentUserAgent = _uiState.value.userAgent
        val currentReferer = _uiState.value.referer

        if (currentUrl.isEmpty()) {
            _uiState.update { it.copy(errorMessage = "URL cannot be empty to save preset.") }
            return
        }

        // Simple dialog for preset name, for now, use URL as name
        val presetName = currentUrl.substringAfterLast("/")
            .substringBefore("?")
            .ifEmpty { "Preset ${System.currentTimeMillis()}" }

        val newPreset = Preset(presetName, currentUrl, currentUserAgent, currentReferer)
        presetManager.savePreset(newPreset)
        loadPresets() // Refresh the list
        _uiState.update { it.copy(errorMessage = "Preset '$presetName' saved.") }
    }

    fun loadPreset(preset: Preset) {
        _uiState.update {
            it.copy(
                url = preset.url,
                userAgent = preset.userAgent,
                referer = preset.referer,
                errorMessage = ""
            )
        }
        playStream() // Automatically play when a preset is loaded
    }

    fun deletePreset(preset: Preset) {
        presetManager.deletePreset(preset)
        loadPresets() // Refresh the list
        _uiState.update { it.copy(errorMessage = "Preset '${preset.name}' deleted.") }
    }

    private fun loadPresets() {
        _uiState.update { it.copy(presets = presetManager.getPresets()) }
    }

    fun handleIntent(intent: Intent?) {
        intent?.data?.let { uri ->
            _uiState.update { it.copy(url = uri.toString()) }
            playStream()
        }
    }

    override fun onCleared() {
        super.onCleared()
        ExoPlayerHelper.releasePlayer()
    }
}

class MainViewModelFactory(private val applicationContext: android.content.Context) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(MainViewModel::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return MainViewModel(applicationContext) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            MyPlayerTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colors.background
                ) {
                    val context = LocalContext.current
                    val viewModel: MainViewModel = remember {
                        ViewModelProvider(this, MainViewModelFactory(context.applicationContext))
                            .get(MainViewModel::class.java)
                    }

                    // Handle incoming intent on launch
                    DisposableEffect(Unit) {
                        viewModel.handleIntent(intent)
                        onDispose {
                            // Clean up if needed
                        }
                    }

                    PlayerScreen(viewModel = viewModel)
                }
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        // Handle new intents if the activity is already running
        val viewModel: MainViewModel = ViewModelProvider(this, MainViewModelFactory(applicationContext))
            .get(MainViewModel::class.java)
        viewModel.handleIntent(intent)
    }
}
