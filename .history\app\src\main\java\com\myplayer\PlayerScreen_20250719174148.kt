package com.myplayer

import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.ui.PlayerView
import androidx.compose.ui.graphics.Color // Import Color

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun PlayerScreen(viewModel: MainViewModel) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()

    var expanded by remember { mutableStateOf(false) }
    val playerOptions = listOf("ExoPlayer", "Web Player")

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Player View
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(16f / 9f)
                .background(Color.Black) // Changed background to Color.Black
        ) {
            AndroidView(
                factory = { ctx ->
                    FrameLayout(ctx).apply {
                        layoutParams = ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.MATCH_PARENT
                        )
                    }
                },
                update = { frameLayout ->
                    frameLayout.removeAllViews()
                    if (uiState.playerType == "ExoPlayer") {
                        val playerView = PlayerView(context).apply {
                            layoutParams = FrameLayout.LayoutParams(
                                FrameLayout.LayoutParams.MATCH_PARENT,
                                FrameLayout.LayoutParams.MATCH_PARENT
                            )
                            useController = true
                            setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
                        }
                        frameLayout.addView(playerView)
                        viewModel.setExoPlayerView(playerView, frameLayout)
                    } else {
                        viewModel.setWebViewContainer(frameLayout)
                    }
                }
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // URL Input
        OutlinedTextField(
            value = uiState.url,
            onValueChange = { viewModel.updateUrl(it) },
            label = { Text("Stream URL") },
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(8.dp))

        // User-Agent Input
        OutlinedTextField(
            value = uiState.userAgent,
            onValueChange = { viewModel.updateUserAgent(it) },
            label = { Text("User-Agent") },
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Referer Input
        OutlinedTextField(
            value = uiState.referer ?: "",
            onValueChange = { viewModel.updateReferer(it) },
            label = { Text("Referer (Optional)") },
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Player Type Dropdown
        ExposedDropdownMenuBox(
            expanded = expanded,
            onExpandedChange = { expanded = !expanded },
            modifier = Modifier.fillMaxWidth()
        ) {
            OutlinedTextField(
                value = uiState.playerType,
                onValueChange = {},
                readOnly = true,
                label = { Text("Player Type") },
                trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                modifier = Modifier.fillMaxWidth()
            )
            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                playerOptions.forEach { option ->
                    DropdownMenuItem(onClick = {
                        viewModel.updatePlayerType(option)
                        expanded = false
                    }) {
                        Text(option)
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround
        ) {
            Button(onClick = { viewModel.playStream() }) {
                Text("Play")
            }
            Button(onClick = { viewModel.savePreset() }) {
                Text("Save Preset")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Error Message
        if (uiState.errorMessage.isNotEmpty()) {
            Text(
                text = "Error: ${uiState.errorMessage}",
                color = MaterialTheme.colors.error,
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        // Saved Presets
        Text("Saved Presets:", style = MaterialTheme.typography.h6)
        LazyColumn(
            modifier = Modifier.fillMaxWidth()
        ) {
            items(uiState.presets) { preset ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp)
                        .clickable { viewModel.loadPreset(preset) },
                    elevation = 2.dp
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Column {
                            Text(text = preset.name, style = MaterialTheme.typography.subtitle1)
                            Text(text = preset.url, style = MaterialTheme.typography.body2)
                        }
                        Button(onClick = { viewModel.deletePreset(preset) }) {
                            Text("Delete")
                        }
                    }
                }
            }
        }
    }
}
