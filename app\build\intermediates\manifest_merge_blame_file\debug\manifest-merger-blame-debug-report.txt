1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.myplayer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95371bf3f47c5f59d54441c97b8b8dd6\transformed\media3-exoplayer-1.1.1\AndroidManifest.xml:24:5-79
12-->[androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95371bf3f47c5f59d54441c97b8b8dd6\transformed\media3-exoplayer-1.1.1\AndroidManifest.xml:24:22-76
13
14    <permission
14-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.myplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.myplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:7:5-36:19
21        android:allowBackup="true"
21-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:8:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:9:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:fullBackupContent="@xml/backup_rules"
26-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:10:9-54
27        android:icon="@mipmap/ic_launcher"
27-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:11:9-43
28        android:label="@string/app_name"
28-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:12:9-41
29        android:supportsRtl="true"
29-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:13:9-35
30        android:theme="@style/Theme.MyPlayer" >
30-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:14:9-46
31        <activity
31-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:16:9-35:20
32            android:name="com.myplayer.MainActivity"
32-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:17:13-41
33            android:exported="true"
33-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:18:13-36
34            android:theme="@style/Theme.MyPlayer" >
34-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:19:13-50
35            <intent-filter>
35-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:20:13-23:29
36                <action android:name="android.intent.action.MAIN" />
36-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:21:17-69
36-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:21:25-66
37
38                <category android:name="android.intent.category.LAUNCHER" />
38-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:22:17-77
38-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:22:27-74
39            </intent-filter>
40            <intent-filter>
40-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:24:13-34:29
41                <action android:name="android.intent.action.VIEW" />
41-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:25:17-69
41-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:25:25-66
42
43                <category android:name="android.intent.category.DEFAULT" />
43-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:26:17-76
43-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:26:27-73
44                <category android:name="android.intent.category.BROWSABLE" />
44-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:27:17-78
44-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:27:27-75
45
46                <data android:scheme="http" />
46-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:17-47
46-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:23-44
47                <data android:scheme="https" />
47-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:17-47
47-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:23-44
48                <data android:host="*" />
48-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:17-47
48-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:30:23-39
49                <data android:pathPattern=".*\\.m3u8" />
49-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:17-47
49-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:31:23-54
50                <data android:mimeType="video/*" />
50-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:17-47
50-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:32:23-49
51                <data android:mimeType="application/x-mpegURL" />
51-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:17-47
51-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:32:23-49
52            </intent-filter>
53        </activity>
54        <activity
54-->[androidx.compose.ui:ui-test-manifest:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\743a53c64b3ad5237960f0f60aeb667d\transformed\ui-test-manifest-1.4.0\AndroidManifest.xml:23:9-25:39
55            android:name="androidx.activity.ComponentActivity"
55-->[androidx.compose.ui:ui-test-manifest:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\743a53c64b3ad5237960f0f60aeb667d\transformed\ui-test-manifest-1.4.0\AndroidManifest.xml:24:13-63
56            android:exported="true" />
56-->[androidx.compose.ui:ui-test-manifest:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\743a53c64b3ad5237960f0f60aeb667d\transformed\ui-test-manifest-1.4.0\AndroidManifest.xml:25:13-36
57        <activity
57-->[androidx.compose.ui:ui-tooling:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef9dc5600468a050226138b1f48dc60e\transformed\ui-tooling-1.4.0\AndroidManifest.xml:23:9-25:39
58            android:name="androidx.compose.ui.tooling.PreviewActivity"
58-->[androidx.compose.ui:ui-tooling:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef9dc5600468a050226138b1f48dc60e\transformed\ui-tooling-1.4.0\AndroidManifest.xml:24:13-71
59            android:exported="true" />
59-->[androidx.compose.ui:ui-tooling:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef9dc5600468a050226138b1f48dc60e\transformed\ui-tooling-1.4.0\AndroidManifest.xml:25:13-36
60
61        <provider
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
62            android:name="androidx.startup.InitializationProvider"
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
63            android:authorities="com.myplayer.androidx-startup"
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
64            android:exported="false" >
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
65            <meta-data
65-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.emoji2.text.EmojiCompatInitializer"
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
67                android:value="androidx.startup" />
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
68            <meta-data
68-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
69                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
69-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
70                android:value="androidx.startup" />
70-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
71            <meta-data
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
73                android:value="androidx.startup" />
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
74        </provider>
75
76        <receiver
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
77            android:name="androidx.profileinstaller.ProfileInstallReceiver"
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
78            android:directBootAware="false"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
79            android:enabled="true"
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
80            android:exported="true"
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
81            android:permission="android.permission.DUMP" >
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
83                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
86                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
89                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
92                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
93            </intent-filter>
94        </receiver>
95    </application>
96
97</manifest>
