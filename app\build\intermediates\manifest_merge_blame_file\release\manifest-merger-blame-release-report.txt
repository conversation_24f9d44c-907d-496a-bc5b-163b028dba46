1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.myplayer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95371bf3f47c5f59d54441c97b8b8dd6\transformed\media3-exoplayer-1.1.1\AndroidManifest.xml:24:5-79
12-->[androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95371bf3f47c5f59d54441c97b8b8dd6\transformed\media3-exoplayer-1.1.1\AndroidManifest.xml:24:22-76
13
14    <permission
14-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.myplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.myplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:7:5-36:19
21        android:allowBackup="true"
21-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:8:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:9:9-65
24        android:extractNativeLibs="false"
25        android:fullBackupContent="@xml/backup_rules"
25-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:10:9-54
26        android:icon="@mipmap/ic_launcher"
26-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:11:9-43
27        android:label="@string/app_name"
27-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:12:9-41
28        android:supportsRtl="true"
28-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:13:9-35
29        android:theme="@style/Theme.MyPlayer" >
29-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:14:9-46
30        <activity
30-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:16:9-35:20
31            android:name="com.myplayer.MainActivity"
31-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:17:13-41
32            android:exported="true"
32-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:18:13-36
33            android:theme="@style/Theme.MyPlayer" >
33-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:19:13-50
34            <intent-filter>
34-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:20:13-23:29
35                <action android:name="android.intent.action.MAIN" />
35-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:21:17-69
35-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:21:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:22:17-77
37-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:22:27-74
38            </intent-filter>
39            <intent-filter>
39-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:24:13-34:29
40                <action android:name="android.intent.action.VIEW" />
40-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:25:17-69
40-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:25:25-66
41
42                <category android:name="android.intent.category.DEFAULT" />
42-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:26:17-76
42-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:26:27-73
43                <category android:name="android.intent.category.BROWSABLE" />
43-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:27:17-78
43-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:27:27-75
44
45                <data android:scheme="http" />
45-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:17-47
45-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:23-44
46                <data android:scheme="https" />
46-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:17-47
46-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:23-44
47                <data android:host="*" />
47-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:17-47
47-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:30:23-39
48                <data android:pathPattern=".*\\.m3u8" />
48-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:17-47
48-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:31:23-54
49                <data android:mimeType="video/*" />
49-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:17-47
49-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:32:23-49
50                <data android:mimeType="application/x-mpegURL" />
50-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:17-47
50-->C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:32:23-49
51            </intent-filter>
52        </activity>
53
54        <provider
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
55            android:name="androidx.startup.InitializationProvider"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
56            android:authorities="com.myplayer.androidx-startup"
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
57            android:exported="false" >
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
58            <meta-data
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.emoji2.text.EmojiCompatInitializer"
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
60                android:value="androidx.startup" />
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
62-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
63                android:value="androidx.startup" />
63-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
66                android:value="androidx.startup" />
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
67        </provider>
68
69        <receiver
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
70            android:name="androidx.profileinstaller.ProfileInstallReceiver"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
71            android:directBootAware="false"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
72            android:enabled="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
73            android:exported="true"
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
74            android:permission="android.permission.DUMP" >
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
76                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
79                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
82                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
85                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
86            </intent-filter>
87        </receiver>
88    </application>
89
90</manifest>
