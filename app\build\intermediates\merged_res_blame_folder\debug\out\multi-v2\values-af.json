{"logs": [{"outputFile": "com.myplayer.app-mergeDebugResources-47:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\95371bf3f47c5f59d54441c97b8b8dd6\\transformed\\media3-exoplayer-1.1.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,184,250,317,392,462,551,635", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "119,179,245,312,387,457,546,630,702"}, "to": {"startLines": "48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3167,3236,3296,3362,3429,3504,3574,3663,3747", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "3231,3291,3357,3424,3499,3569,3658,3742,3814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b3bcdf5867897ddb0a08993a0c87c689\\transformed\\media3-ui-1.1.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,500,689,776,864,944,1031,1117,1188,1255,1353,1446,1516,1580,1642,1711,1826,1940,2053,2125,2207,2281,2347,2434,2522,2585,2650,2703,2761,2809,2870,2935,3007,3072,3140,3198,3256,3322,3386,3452,3504,3563,3636,3709", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,114,113,112,71,81,73,65,86,87,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "281,495,684,771,859,939,1026,1112,1183,1250,1348,1441,1511,1575,1637,1706,1821,1935,2048,2120,2202,2276,2342,2429,2517,2580,2645,2698,2756,2804,2865,2930,3002,3067,3135,3193,3251,3317,3381,3447,3499,3558,3631,3704,3759"}, "to": {"startLines": "2,11,15,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,550,1206,1293,1381,1461,1548,1634,1705,1772,1870,1963,2033,2097,2159,2228,2343,2457,2570,2642,2724,2798,2864,2951,3039,3102,3819,3872,3930,3978,4039,4104,4176,4241,4309,4367,4425,4491,4555,4621,4673,4732,4805,4878", "endLines": "10,14,18,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,114,113,112,71,81,73,65,86,87,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "331,545,734,1288,1376,1456,1543,1629,1700,1767,1865,1958,2028,2092,2154,2223,2338,2452,2565,2637,2719,2793,2859,2946,3034,3097,3162,3867,3925,3973,4034,4099,4171,4236,4304,4362,4420,4486,4550,4616,4668,4727,4800,4873,4928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\443fb41afb655d27c0a0e8d699a9585a\\transformed\\ui-1.4.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,486,572,648,739,829,915,979,1044,1122,1203,1274,1355,1425", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "196,283,380,481,567,643,734,824,910,974,1039,1117,1198,1269,1350,1420,1540"}, "to": {"startLines": "19,20,21,22,23,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "739,835,922,1019,1120,4933,5009,5100,5190,5276,5340,5405,5483,5564,5736,5817,5887", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "830,917,1014,1115,1201,5004,5095,5185,5271,5335,5400,5478,5559,5630,5812,5882,6002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0b9306a62c95bc1ca3eb6c6fcf39fb4\\transformed\\core-1.9.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "5635", "endColumns": "100", "endOffsets": "5731"}}]}]}