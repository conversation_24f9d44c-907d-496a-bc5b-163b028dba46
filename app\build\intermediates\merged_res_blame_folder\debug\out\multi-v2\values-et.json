{"logs": [{"outputFile": "com.myplayer.app-mergeDebugResources-47:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\95371bf3f47c5f59d54441c97b8b8dd6\\transformed\\media3-exoplayer-1.1.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,263,337,419,490,580,672", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "124,191,258,332,414,485,575,667,744"}, "to": {"startLines": "48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3153,3227,3294,3361,3435,3517,3588,3678,3770", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "3222,3289,3356,3430,3512,3583,3673,3765,3842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\443fb41afb655d27c0a0e8d699a9585a\\transformed\\ui-1.4.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,963,1029,1111,1196,1268,1345,1416", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,958,1024,1106,1191,1263,1340,1411,1533"}, "to": {"startLines": "19,20,21,22,23,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "752,845,925,1021,1116,4976,5054,5145,5236,5320,5388,5454,5536,5621,5794,5871,5942", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "840,920,1016,1111,1193,5049,5140,5231,5315,5383,5449,5531,5616,5688,5866,5937,6059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b3bcdf5867897ddb0a08993a0c87c689\\transformed\\media3-ui-1.1.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,514,702,783,864,940,1031,1124,1194,1258,1342,1425,1490,1554,1617,1687,1807,1925,2044,2116,2200,2269,2338,2432,2526,2591,2657,2710,2770,2818,2879,2944,3014,3079,3145,3209,3269,3334,3399,3465,3517,3579,3655,3731", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,119,117,118,71,83,68,68,93,93,64,65,52,59,47,60,64,69,64,65,63,59,64,64,65,51,61,75,75,54", "endOffsets": "318,509,697,778,859,935,1026,1119,1189,1253,1337,1420,1485,1549,1612,1682,1802,1920,2039,2111,2195,2264,2333,2427,2521,2586,2652,2705,2765,2813,2874,2939,3009,3074,3140,3204,3264,3329,3394,3460,3512,3574,3650,3726,3781"}, "to": {"startLines": "2,11,15,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,373,564,1198,1279,1360,1436,1527,1620,1690,1754,1838,1921,1986,2050,2113,2183,2303,2421,2540,2612,2696,2765,2834,2928,3022,3087,3847,3900,3960,4008,4069,4134,4204,4269,4335,4399,4459,4524,4589,4655,4707,4769,4845,4921", "endLines": "10,14,18,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,119,117,118,71,83,68,68,93,93,64,65,52,59,47,60,64,69,64,65,63,59,64,64,65,51,61,75,75,54", "endOffsets": "368,559,747,1274,1355,1431,1522,1615,1685,1749,1833,1916,1981,2045,2108,2178,2298,2416,2535,2607,2691,2760,2829,2923,3017,3082,3148,3895,3955,4003,4064,4129,4199,4264,4330,4394,4454,4519,4584,4650,4702,4764,4840,4916,4971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0b9306a62c95bc1ca3eb6c6fcf39fb4\\transformed\\core-1.9.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "5693", "endColumns": "100", "endOffsets": "5789"}}]}]}