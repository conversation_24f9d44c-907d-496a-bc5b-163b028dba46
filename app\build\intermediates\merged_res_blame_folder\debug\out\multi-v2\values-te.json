{"logs": [{"outputFile": "com.myplayer.app-mergeDebugResources-47:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\95371bf3f47c5f59d54441c97b8b8dd6\\transformed\\media3-exoplayer-1.1.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,195,268,336,416,493,594,687", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "122,190,263,331,411,488,589,682,760"}, "to": {"startLines": "48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3329,3401,3469,3542,3610,3690,3767,3868,3961", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "3396,3464,3537,3605,3685,3762,3863,3956,4034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b3bcdf5867897ddb0a08993a0c87c689\\transformed\\media3-ui-1.1.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,502,695,787,879,968,1068,1172,1249,1314,1406,1498,1569,1639,1700,1770,1907,2039,2174,2249,2333,2408,2479,2573,2667,2731,2810,2863,2921,2969,3030,3097,3159,3224,3291,3350,3412,3478,3542,3609,3663,3723,3797,3871", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,91,88,99,103,76,64,91,91,70,69,60,69,136,131,134,74,83,74,70,93,93,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "281,497,690,782,874,963,1063,1167,1244,1309,1401,1493,1564,1634,1695,1765,1902,2034,2169,2244,2328,2403,2474,2568,2662,2726,2805,2858,2916,2964,3025,3092,3154,3219,3286,3345,3407,3473,3537,3604,3658,3718,3792,3866,3920"}, "to": {"startLines": "2,11,15,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,552,1214,1306,1398,1487,1587,1691,1768,1833,1925,2017,2088,2158,2219,2289,2426,2558,2693,2768,2852,2927,2998,3092,3186,3250,4039,4092,4150,4198,4259,4326,4388,4453,4520,4579,4641,4707,4771,4838,4892,4952,5026,5100", "endLines": "10,14,18,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "17,12,12,91,91,88,99,103,76,64,91,91,70,69,60,69,136,131,134,74,83,74,70,93,93,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "331,547,740,1301,1393,1482,1582,1686,1763,1828,1920,2012,2083,2153,2214,2284,2421,2553,2688,2763,2847,2922,2993,3087,3181,3245,3324,4087,4145,4193,4254,4321,4383,4448,4515,4574,4636,4702,4766,4833,4887,4947,5021,5095,5149"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0b9306a62c95bc1ca3eb6c6fcf39fb4\\transformed\\core-1.9.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "5908", "endColumns": "100", "endOffsets": "6004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\443fb41afb655d27c0a0e8d699a9585a\\transformed\\ui-1.4.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,485,574,663,759,847,931,1004,1077,1161,1251,1328,1405,1474", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "196,283,380,480,569,658,754,842,926,999,1072,1156,1246,1323,1400,1469,1586"}, "to": {"startLines": "19,20,21,22,23,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "745,841,928,1025,1125,5154,5243,5339,5427,5511,5584,5657,5741,5831,6009,6086,6155", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "836,923,1020,1120,1209,5238,5334,5422,5506,5579,5652,5736,5826,5903,6081,6150,6267"}}]}]}