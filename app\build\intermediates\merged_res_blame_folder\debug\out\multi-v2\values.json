{"logs": [{"outputFile": "com.myplayer.app-mergeDebugResources-47:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\01f066987bee4a8427cf3d60ef0773bb\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "284,289", "startColumns": "4,4", "startOffsets": "16648,16900", "endColumns": "53,66", "endOffsets": "16697,16962"}}, {"source": "C:\\Users\\<USER>\\Desktop\\myPlayer\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "17", "endColumns": "45", "endOffsets": "58"}, "to": {"startLines": "325", "startColumns": "4", "startOffsets": "19064", "endColumns": "45", "endOffsets": "19105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\103dd26deaedd313bf92b1242ad56ed5\\transformed\\activity-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "290,308", "startColumns": "4,4", "startOffsets": "16967,17911", "endColumns": "41,59", "endOffsets": "17004,17966"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a369e9b05bfc9675d1a3b87b09fd3458\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "324", "startColumns": "4", "startOffsets": "18981", "endColumns": "82", "endOffsets": "19059"}}, {"source": "C:\\Users\\<USER>\\Desktop\\myPlayer\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "59", "endColumns": "85", "endOffsets": "140"}, "to": {"startLines": "585", "startColumns": "4", "startOffsets": "35026", "endColumns": "84", "endOffsets": "35106"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\24e93206e9e626e34f85f58e5f4e9627\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "36,137,138,139,140,141,142,285,811", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1519,7051,7110,7158,7214,7289,7365,16702,43052", "endLines": "36,137,138,139,140,141,142,285,831", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "1570,7105,7153,7209,7284,7360,7432,16763,43887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9b0abddd5103bd96040e99123d7752f\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "309", "startColumns": "4", "startOffsets": "17971", "endColumns": "53", "endOffsets": "18020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1ad5ce4cfe177ac66043caf8989aacc8\\transformed\\lifecycle-viewmodel-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "310", "startColumns": "4", "startOffsets": "18025", "endColumns": "49", "endOffsets": "18070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f551cfd33cef484fbd97e55a86c5963e\\transformed\\media-1.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,350,410,476,598,659,725", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "139,210,283,345,405,471,593,654,720,787"}, "to": {"startLines": "92,93,94,288,312,574,576,577,582,584", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4251,4340,4411,16838,18128,34204,34380,34502,34764,34959", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "4335,4406,4479,16895,18183,34265,34497,34558,34825,35021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\95371bf3f47c5f59d54441c97b8b8dd6\\transformed\\media3-exoplayer-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "357,358,359,360,361,362,363,364,365", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "21586,21656,21718,21783,21847,21924,21989,22079,22163", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "21651,21713,21778,21842,21919,21984,22074,22158,22227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b3bcdf5867897ddb0a08993a0c87c689\\transformed\\media3-ui-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,35,40,47,48,49,50,51,52,57,58,59,60,61,62,63,64,65,66,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,213,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,266,270,274,278,282,286,290,294,295,301,312,316,320,324,328,332,336,340,344,348,352,356,367,372,377,382,393,401,411,415,419,423,426,442,468,497", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1140,1190,1255,1312,1359,1414,1562,1800,1849,1910,1970,2026,2086,2256,2316,2369,2426,2481,2537,2594,2643,2694,2753,3040,3105,3163,3212,3260,3311,3368,3425,3487,3554,3625,3697,3741,3798,3854,3917,3990,4060,4119,4176,4223,4278,4323,4372,4427,4481,4531,4582,4636,4695,4745,4803,4859,4912,4975,5040,5103,5155,5215,5279,5345,5403,5475,5536,5606,5676,5741,5806,5877,5972,6077,6180,6261,6344,6425,6514,6607,6700,6793,6878,6973,7066,7143,7235,7313,7393,7471,7557,7639,7732,7810,7901,7982,8071,8174,8275,8359,8455,8552,8647,8740,8832,8925,9018,9111,9194,9281,9376,9469,9550,9645,9738,9815,9859,9900,9945,9993,10037,10080,10129,10176,10220,10276,10329,10371,10418,10466,10526,10564,10614,10658,10708,10760,10798,10845,10892,10933,10972,11010,11054,11102,11144,11182,11224,11278,11325,11362,11411,11453,11494,11535,11577,11620,11658,11694,11772,11850,12147,12417,12499,12581,12723,12801,12888,12973,13040,13103,13195,13287,13352,13415,13477,13548,13658,13769,13879,13946,14026,14097,14164,14249,14334,14397,14485,14549,14691,14791,14839,14982,15045,15107,15172,15243,15301,15359,15425,15489,15555,15607,15669,15745,15821,15875,16154,16385,16595,16808,17018,17240,17456,17660,17698,18052,18839,19080,19320,19577,19830,20083,20318,20565,20804,21048,21269,21464,22039,22330,22626,22929,23498,24032,24506,24717,24917,25093,25201,25777,26722,27772", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,34,39,46,47,48,49,50,51,56,57,58,59,60,61,62,63,64,65,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,212,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,265,269,273,277,281,285,289,293,294,300,311,315,319,323,327,331,335,339,343,347,351,355,366,371,376,381,392,400,410,414,418,422,425,441,467,496,536", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1135,1185,1250,1307,1354,1409,1557,1795,1844,1905,1965,2021,2081,2251,2311,2364,2421,2476,2532,2589,2638,2689,2748,3035,3100,3158,3207,3255,3306,3363,3420,3482,3549,3620,3692,3736,3793,3849,3912,3985,4055,4114,4171,4218,4273,4318,4367,4422,4476,4526,4577,4631,4690,4740,4798,4854,4907,4970,5035,5098,5150,5210,5274,5340,5398,5470,5531,5601,5671,5736,5801,5872,5967,6072,6175,6256,6339,6420,6509,6602,6695,6788,6873,6968,7061,7138,7230,7308,7388,7466,7552,7634,7727,7805,7896,7977,8066,8169,8270,8354,8450,8547,8642,8735,8827,8920,9013,9106,9189,9276,9371,9464,9545,9640,9733,9810,9854,9895,9940,9988,10032,10075,10124,10171,10215,10271,10324,10366,10413,10461,10521,10559,10609,10653,10703,10755,10793,10840,10887,10928,10967,11005,11049,11097,11139,11177,11219,11273,11320,11357,11406,11448,11489,11530,11572,11615,11653,11689,11767,11845,12142,12412,12494,12576,12718,12796,12883,12968,13035,13098,13190,13282,13347,13410,13472,13543,13653,13764,13874,13941,14021,14092,14159,14244,14329,14392,14480,14544,14686,14786,14834,14977,15040,15102,15167,15238,15296,15354,15420,15484,15550,15602,15664,15740,15816,15870,16149,16380,16590,16803,17013,17235,17451,17655,17693,18047,18834,19075,19315,19572,19825,20078,20313,20560,20799,21043,21264,21459,22034,22325,22621,22924,23493,24027,24501,24712,24912,25088,25196,25772,26717,27767,29125"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,33,34,35,37,42,49,50,51,52,53,54,59,60,61,62,63,64,65,66,67,68,75,76,77,78,79,82,83,84,85,86,87,88,89,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,313,314,316,320,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,400,405,409,413,417,421,425,429,433,434,440,451,455,459,463,467,471,475,479,483,487,491,495,506,511,516,521,532,540,550,554,558,588,607,716,742,771", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1133,1185,1235,1360,1417,1464,1575,1723,1961,2010,2071,2131,2187,2247,2417,2477,2530,2587,2642,2698,2755,2804,2855,2914,3201,3266,3324,3373,3421,3632,3689,3746,3808,3875,3946,4018,4062,4972,5028,5091,5164,5234,5293,5350,5397,5452,5497,5546,5601,5655,5705,5756,5810,5869,5919,5977,6033,6086,6149,6214,6277,6329,6389,6453,6519,6577,6649,6710,6780,6850,6915,6980,8415,8510,8615,8718,8799,8882,8963,9052,9145,9238,9331,9416,9511,9604,9681,9773,9851,9931,10009,10095,10177,10270,10348,10439,10520,10609,10712,10813,10897,10993,11090,11185,11278,11370,11463,11556,11649,11732,11819,11914,12007,12088,12183,12276,14663,14707,14748,14793,14841,14885,14928,14977,15024,15068,15124,15177,15219,15266,15314,15374,15412,15462,15506,15556,15608,15646,15693,15740,15781,15820,15858,15902,15950,15992,16030,16072,16126,16173,16210,16259,16301,16342,16383,16425,16468,16506,18188,18266,18414,18711,19454,19536,19618,19760,19838,19925,20010,20077,20140,20232,20324,20389,20452,20514,20585,20695,20806,20916,20983,21063,21134,21201,21286,21371,21434,21522,22232,22374,22474,22522,22665,22728,22790,22855,22926,22984,23042,23108,23172,23238,23290,23352,23428,23504,24362,24641,24872,25082,25295,25505,25727,25943,26147,26185,26539,27326,27567,27807,28064,28317,28570,28805,29052,29291,29535,29756,29951,30526,30817,31113,31416,31985,32519,32993,33204,33404,35250,35862,39699,40644,41694", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,33,34,35,41,48,49,50,51,52,53,58,59,60,61,62,63,64,65,66,67,74,75,76,77,78,79,82,83,84,85,86,87,88,89,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,313,314,319,323,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,404,408,412,416,420,424,428,432,433,439,450,454,458,462,466,470,474,478,482,486,490,494,505,510,515,520,531,539,549,553,557,561,590,622,741,770,810", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1180,1230,1295,1412,1459,1514,1718,1956,2005,2066,2126,2182,2242,2412,2472,2525,2582,2637,2693,2750,2799,2850,2909,3196,3261,3319,3368,3416,3467,3684,3741,3803,3870,3941,4013,4057,4114,5023,5086,5159,5229,5288,5345,5392,5447,5492,5541,5596,5650,5700,5751,5805,5864,5914,5972,6028,6081,6144,6209,6272,6324,6384,6448,6514,6572,6644,6705,6775,6845,6910,6975,7046,8505,8610,8713,8794,8877,8958,9047,9140,9233,9326,9411,9506,9599,9676,9768,9846,9926,10004,10090,10172,10265,10343,10434,10515,10604,10707,10808,10892,10988,11085,11180,11273,11365,11458,11551,11644,11727,11814,11909,12002,12083,12178,12271,12348,14702,14743,14788,14836,14880,14923,14972,15019,15063,15119,15172,15214,15261,15309,15369,15407,15457,15501,15551,15603,15641,15688,15735,15776,15815,15853,15897,15945,15987,16025,16067,16121,16168,16205,16254,16296,16337,16378,16420,16463,16501,16537,18261,18339,18706,18976,19531,19613,19755,19833,19920,20005,20072,20135,20227,20319,20384,20447,20509,20580,20690,20801,20911,20978,21058,21129,21196,21281,21366,21429,21517,21581,22369,22469,22517,22660,22723,22785,22850,22921,22979,23037,23103,23167,23233,23285,23347,23423,23499,23553,24636,24867,25077,25290,25500,25722,25938,26142,26180,26534,27321,27562,27802,28059,28312,28565,28800,29047,29286,29530,29751,29946,30521,30812,31108,31411,31980,32514,32988,33199,33399,33575,35353,36433,40639,41689,43047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0b9306a62c95bc1ca3eb6c6fcf39fb4\\transformed\\core-1.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,89,90,94,95,96,97,103,113,146,167,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,344,407,477,545,617,687,748,822,895,956,1017,1079,1143,1205,1266,1334,1434,1494,1560,1633,1702,1759,1811,1873,1945,2021,3996,4031,4066,4121,4184,4239,4297,4355,4416,4479,4536,4587,4637,4698,4755,4821,4855,4890,4925,4995,5066,5183,5384,5494,5695,5824,5896,5963,6166,6467,8198,8879,9561", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,88,89,93,94,95,96,102,112,145,166,199,205", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,339,402,472,540,612,682,743,817,890,951,1012,1074,1138,1200,1261,1329,1429,1489,1555,1628,1697,1754,1806,1868,1940,2016,2081,4026,4061,4116,4179,4234,4292,4350,4411,4474,4531,4582,4632,4693,4750,4816,4850,4885,4920,4990,5061,5178,5379,5489,5690,5819,5891,5958,6161,6462,8193,8874,9556,9723"}, "to": {"startLines": "32,80,81,90,91,95,96,97,98,99,100,101,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,202,203,204,286,287,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,315,393,569,570,575,578,583,586,587,591,597,623,656,677,710", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1300,3472,3544,4119,4188,4484,4554,4622,4694,4764,4825,4899,7437,7498,7559,7621,7685,7747,7808,7876,7976,8036,8102,8175,8244,8301,8353,12353,12425,12501,16768,16803,17009,17064,17127,17182,17240,17298,17359,17422,17479,17530,17580,17641,17698,17764,17798,17833,18344,24006,33886,34003,34270,34563,34830,35111,35183,35358,35561,36438,38169,38850,39532", "endLines": "32,80,81,90,91,95,96,97,98,99,100,101,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,202,203,204,286,287,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,315,393,569,573,575,581,583,586,587,596,606,655,676,709,715", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1355,3539,3627,4183,4246,4549,4617,4689,4759,4820,4894,4967,7493,7554,7616,7680,7742,7803,7871,7971,8031,8097,8170,8239,8296,8348,8410,12420,12496,12561,16798,16833,17059,17122,17177,17235,17293,17354,17417,17474,17525,17575,17636,17693,17759,17793,17828,17863,18409,24072,33998,34199,34375,34759,34954,35178,35245,35556,35857,38164,38845,39527,39694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\443fb41afb655d27c0a0e8d699a9585a\\transformed\\ui-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,282,283,311,326,327,328,329,330,384,385,386,387,388,389,390,391,392,394,395,396,397,562,565", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12566,12625,12684,12744,12804,12864,12924,12984,13044,13104,13164,13224,13284,13343,13403,13463,13523,13583,13643,13703,13763,13823,13883,13943,14002,14062,14122,14181,14240,14299,14358,14417,14476,14550,14608,16542,16593,18075,19110,19175,19229,19295,19396,23558,23610,23670,23732,23786,23822,23856,23906,23960,24077,24124,24160,24250,33580,33691", "endLines": "205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,282,283,311,326,327,328,329,330,384,385,386,387,388,389,390,391,392,394,395,396,399,564,568", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "12620,12679,12739,12799,12859,12919,12979,13039,13099,13159,13219,13279,13338,13398,13458,13518,13578,13638,13698,13758,13818,13878,13938,13997,14057,14117,14176,14235,14294,14353,14412,14471,14545,14603,14658,16588,16643,18123,19170,19224,19290,19391,19449,23605,23665,23727,23781,23817,23851,23901,23955,24001,24119,24155,24245,24357,33686,33881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eb62e5fae023fdc2953dd5c3d44ce869\\transformed\\lifecycle-runtime-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "307", "startColumns": "4", "startOffsets": "17868", "endColumns": "42", "endOffsets": "17906"}}]}]}