{"logs": [{"outputFile": "com.myplayer.app-mergeReleaseResources-44:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b3bcdf5867897ddb0a08993a0c87c689\\transformed\\media3-ui-1.1.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,11,19,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,1314,1393,1471,1547,1641,1733,1807,1872,1964,2054,2124,2188,2251,2320,2428,2537,2652,2718,2801,2873,2945,3037,3128,3192,3255,3308,3379,3434,3495,3553,3627,3691,3755,3815,3880,3944,4006,4072,4124,4181,4252,4323", "endLines": "10,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "344,844,1309,1388,1466,1542,1636,1728,1802,1867,1959,2049,2119,2183,2246,2315,2423,2532,2647,2713,2796,2868,2940,3032,3123,3187,3250,3303,3374,3429,3490,3548,3622,3686,3750,3810,3875,3939,4001,4067,4119,4176,4247,4318,4374"}, "to": {"startLines": "2,11,19,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,399,899,1814,1893,1971,2047,2141,2233,2307,2372,2464,2554,2624,2688,2751,2820,2928,3037,3152,3218,3301,3373,3445,3537,3628,3692,4451,4504,4575,4630,4691,4749,4823,4887,4951,5011,5076,5140,5202,5268,5320,5377,5448,5519", "endLines": "10,18,26,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "394,894,1359,1888,1966,2042,2136,2228,2302,2367,2459,2549,2619,2683,2746,2815,2923,3032,3147,3213,3296,3368,3440,3532,3623,3687,3750,4499,4570,4625,4686,4744,4818,4882,4946,5006,5071,5135,5197,5263,5315,5372,5443,5514,5570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\443fb41afb655d27c0a0e8d699a9585a\\transformed\\ui-1.4.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,372,470,555,636,742,826,907,978,1045,1126,1209,1279,1355,1429", "endColumns": "88,82,94,97,84,80,105,83,80,70,66,80,82,69,75,73,120", "endOffsets": "189,272,367,465,550,631,737,821,902,973,1040,1121,1204,1274,1350,1424,1545"}, "to": {"startLines": "27,28,29,30,31,83,84,85,86,87,88,89,90,91,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1364,1453,1536,1631,1729,5575,5656,5762,5846,5927,5998,6065,6146,6229,6400,6476,6550", "endColumns": "88,82,94,97,84,80,105,83,80,70,66,80,82,69,75,73,120", "endOffsets": "1448,1531,1626,1724,1809,5651,5757,5841,5922,5993,6060,6141,6224,6294,6471,6545,6666"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0b9306a62c95bc1ca3eb6c6fcf39fb4\\transformed\\core-1.9.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "92", "startColumns": "4", "startOffsets": "6299", "endColumns": "100", "endOffsets": "6395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\95371bf3f47c5f59d54441c97b8b8dd6\\transformed\\media3-exoplayer-1.1.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,308,390,471,572,667", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "115,174,241,303,385,466,567,662,746"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3755,3820,3879,3946,4008,4090,4171,4272,4367", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "3815,3874,3941,4003,4085,4166,4267,4362,4446"}}]}]}