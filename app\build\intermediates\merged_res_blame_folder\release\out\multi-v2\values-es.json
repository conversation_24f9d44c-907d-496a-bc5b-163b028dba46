{"logs": [{"outputFile": "com.myplayer.app-mergeReleaseResources-44:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0b9306a62c95bc1ca3eb6c6fcf39fb4\\transformed\\core-1.9.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "5728", "endColumns": "100", "endOffsets": "5824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b3bcdf5867897ddb0a08993a0c87c689\\transformed\\media3-ui-1.1.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,744,831,916,1012,1108,1183,1251,1346,1441,1507,1576,1642,1713,1821,1927,2034,2104,2191,2261,2341,2431,2522,2588,2652,2705,2763,2811,2870,2935,2997,3063,3135,3199,3260,3326,3391,3457,3510,3575,3654,3733", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,466,653,739,826,911,1007,1103,1178,1246,1341,1436,1502,1571,1637,1708,1816,1922,2029,2099,2186,2256,2336,2426,2517,2583,2647,2700,2758,2806,2865,2930,2992,3058,3130,3194,3255,3321,3386,3452,3505,3570,3649,3728,3786"}, "to": {"startLines": "2,11,15,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,1176,1262,1349,1434,1530,1626,1701,1769,1864,1959,2025,2094,2160,2231,2339,2445,2552,2622,2709,2779,2859,2949,3040,3106,3841,3894,3952,4000,4059,4124,4186,4252,4324,4388,4449,4515,4580,4646,4699,4764,4843,4922", "endLines": "10,14,18,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "330,516,703,1257,1344,1429,1525,1621,1696,1764,1859,1954,2020,2089,2155,2226,2334,2440,2547,2617,2704,2774,2854,2944,3035,3101,3165,3889,3947,3995,4054,4119,4181,4247,4319,4383,4444,4510,4575,4641,4694,4759,4838,4917,4975"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\95371bf3f47c5f59d54441c97b8b8dd6\\transformed\\media3-exoplayer-1.1.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,201,266,340,417,484,571,657", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "133,196,261,335,412,479,566,652,721"}, "to": {"startLines": "48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3170,3253,3316,3381,3455,3532,3599,3686,3772", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "3248,3311,3376,3450,3527,3594,3681,3767,3836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\443fb41afb655d27c0a0e8d699a9585a\\transformed\\ui-1.4.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,997,1067,1153,1244,1321,1403,1473", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,992,1062,1148,1239,1316,1398,1468,1589"}, "to": {"startLines": "19,20,21,22,23,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "708,804,886,984,1087,4980,5059,5152,5244,5331,5404,5474,5560,5651,5829,5911,5981", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "799,881,979,1082,1171,5054,5147,5239,5326,5399,5469,5555,5646,5723,5906,5976,6097"}}]}]}