{"logs": [{"outputFile": "com.myplayer.app-mergeReleaseResources-44:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\443fb41afb655d27c0a0e8d699a9585a\\transformed\\ui-1.4.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,991,1060,1145,1235,1311,1387,1459", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,986,1055,1140,1230,1306,1382,1454,1576"}, "to": {"startLines": "19,20,21,22,23,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "708,803,886,983,1082,4969,5048,5145,5236,5323,5395,5464,5549,5639,5816,5892,5964", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "798,881,978,1077,1163,5043,5140,5231,5318,5390,5459,5544,5634,5710,5887,5959,6081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\95371bf3f47c5f59d54441c97b8b8dd6\\transformed\\media3-exoplayer-1.1.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,194,261,332,414,496,591,680", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "125,189,256,327,409,491,586,675,754"}, "to": {"startLines": "48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3136,3211,3275,3342,3413,3495,3577,3672,3761", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "3206,3270,3337,3408,3490,3572,3667,3756,3835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b3bcdf5867897ddb0a08993a0c87c689\\transformed\\media3-ui-1.1.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,739,822,895,989,1079,1153,1220,1317,1414,1480,1549,1616,1687,1798,1909,2019,2086,2172,2245,2319,2406,2495,2559,2626,2679,2737,2785,2846,2911,2979,3044,3113,3177,3238,3304,3369,3435,3488,3548,3622,3696", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,110,110,109,66,85,72,73,86,88,63,66,52,57,47,60,64,67,64,68,63,60,65,64,65,52,59,73,73,58", "endOffsets": "280,466,653,734,817,890,984,1074,1148,1215,1312,1409,1475,1544,1611,1682,1793,1904,2014,2081,2167,2240,2314,2401,2490,2554,2621,2674,2732,2780,2841,2906,2974,3039,3108,3172,3233,3299,3364,3430,3483,3543,3617,3691,3750"}, "to": {"startLines": "2,11,15,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,1168,1249,1332,1405,1499,1589,1663,1730,1827,1924,1990,2059,2126,2197,2308,2419,2529,2596,2682,2755,2829,2916,3005,3069,3840,3893,3951,3999,4060,4125,4193,4258,4327,4391,4452,4518,4583,4649,4702,4762,4836,4910", "endLines": "10,14,18,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,110,110,109,66,85,72,73,86,88,63,66,52,57,47,60,64,67,64,68,63,60,65,64,65,52,59,73,73,58", "endOffsets": "330,516,703,1244,1327,1400,1494,1584,1658,1725,1822,1919,1985,2054,2121,2192,2303,2414,2524,2591,2677,2750,2824,2911,3000,3064,3131,3888,3946,3994,4055,4120,4188,4253,4322,4386,4447,4513,4578,4644,4697,4757,4831,4905,4964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0b9306a62c95bc1ca3eb6c6fcf39fb4\\transformed\\core-1.9.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "5715", "endColumns": "100", "endOffsets": "5811"}}]}]}