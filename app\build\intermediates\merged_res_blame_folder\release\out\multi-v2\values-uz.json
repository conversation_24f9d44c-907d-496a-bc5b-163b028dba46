{"logs": [{"outputFile": "com.myplayer.app-mergeReleaseResources-44:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b3bcdf5867897ddb0a08993a0c87c689\\transformed\\media3-ui-1.1.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,468,644,730,825,908,1006,1105,1180,1248,1349,1450,1515,1578,1641,1713,1841,1973,2100,2177,2251,2324,2399,2489,2588,2657,2723,2776,2836,2884,2945,3006,3077,3137,3205,3268,3333,3399,3463,3529,3581,3642,3717,3792", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,127,131,126,76,73,72,74,89,98,68,65,52,59,47,60,60,70,59,67,62,64,65,63,65,51,60,74,74,52", "endOffsets": "280,463,639,725,820,903,1001,1100,1175,1243,1344,1445,1510,1573,1636,1708,1836,1968,2095,2172,2246,2319,2394,2484,2583,2652,2718,2771,2831,2879,2940,3001,3072,3132,3200,3263,3328,3394,3458,3524,3576,3637,3712,3787,3840"}, "to": {"startLines": "2,11,15,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,518,1185,1271,1366,1449,1547,1646,1721,1789,1890,1991,2056,2119,2182,2254,2382,2514,2641,2718,2792,2865,2940,3030,3129,3198,3952,4005,4065,4113,4174,4235,4306,4366,4434,4497,4562,4628,4692,4758,4810,4871,4946,5021", "endLines": "10,14,18,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,127,131,126,76,73,72,74,89,98,68,65,52,59,47,60,60,70,59,67,62,64,65,63,65,51,60,74,74,52", "endOffsets": "330,513,689,1266,1361,1444,1542,1641,1716,1784,1885,1986,2051,2114,2177,2249,2377,2509,2636,2713,2787,2860,2935,3025,3124,3193,3259,4000,4060,4108,4169,4230,4301,4361,4429,4492,4557,4623,4687,4753,4805,4866,4941,5016,5069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\443fb41afb655d27c0a0e8d699a9585a\\transformed\\ui-1.4.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,289,393,500,596,679,769,862,945,1013,1080,1161,1244,1318,1401,1469", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "199,284,388,495,591,674,764,857,940,1008,1075,1156,1239,1313,1396,1464,1581"}, "to": {"startLines": "19,20,21,22,23,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "694,793,878,982,1089,5074,5157,5247,5340,5423,5491,5558,5639,5722,5897,5980,6048", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "788,873,977,1084,1180,5152,5242,5335,5418,5486,5553,5634,5717,5791,5975,6043,6160"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0b9306a62c95bc1ca3eb6c6fcf39fb4\\transformed\\core-1.9.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "5796", "endColumns": "100", "endOffsets": "5892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\95371bf3f47c5f59d54441c97b8b8dd6\\transformed\\media3-exoplayer-1.1.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,186,256,320,399,467,569,663", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "115,181,251,315,394,462,564,658,738"}, "to": {"startLines": "48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3264,3329,3395,3465,3529,3608,3676,3778,3872", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "3324,3390,3460,3524,3603,3671,3773,3867,3947"}}]}]}