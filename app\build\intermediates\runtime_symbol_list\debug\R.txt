int array exo_controls_playback_speeds 0x7f010000
int attr ad_marker_color 0x7f020000
int attr ad_marker_width 0x7f020001
int attr alpha 0x7f020002
int attr animation_enabled 0x7f020003
int attr artwork_display_mode 0x7f020004
int attr auto_show 0x7f020005
int attr backgroundTint 0x7f020006
int attr bar_gravity 0x7f020007
int attr bar_height 0x7f020008
int attr buffered_color 0x7f020009
int attr controller_layout_id 0x7f02000a
int attr default_artwork 0x7f02000b
int attr fastScrollEnabled 0x7f02000c
int attr fastScrollHorizontalThumbDrawable 0x7f02000d
int attr fastScrollHorizontalTrackDrawable 0x7f02000e
int attr fastScrollVerticalThumbDrawable 0x7f02000f
int attr fastScrollVerticalTrackDrawable 0x7f020010
int attr font 0x7f020011
int attr fontProviderAuthority 0x7f020012
int attr fontProviderCerts 0x7f020013
int attr fontProviderFetchStrategy 0x7f020014
int attr fontProviderFetchTimeout 0x7f020015
int attr fontProviderPackage 0x7f020016
int attr fontProviderQuery 0x7f020017
int attr fontProviderSystemFontFamily 0x7f020018
int attr fontStyle 0x7f020019
int attr fontVariationSettings 0x7f02001a
int attr fontWeight 0x7f02001b
int attr hide_during_ads 0x7f02001c
int attr hide_on_touch 0x7f02001d
int attr keep_content_on_player_reset 0x7f02001e
int attr lStar 0x7f02001f
int attr layoutManager 0x7f020020
int attr nestedScrollViewStyle 0x7f020021
int attr played_ad_marker_color 0x7f020022
int attr played_color 0x7f020023
int attr player_layout_id 0x7f020024
int attr queryPatterns 0x7f020025
int attr recyclerViewStyle 0x7f020026
int attr repeat_toggle_modes 0x7f020027
int attr resize_mode 0x7f020028
int attr reverseLayout 0x7f020029
int attr scrubber_color 0x7f02002a
int attr scrubber_disabled_size 0x7f02002b
int attr scrubber_dragged_size 0x7f02002c
int attr scrubber_drawable 0x7f02002d
int attr scrubber_enabled_size 0x7f02002e
int attr shortcutMatchRequired 0x7f02002f
int attr show_buffering 0x7f020030
int attr show_fastforward_button 0x7f020031
int attr show_next_button 0x7f020032
int attr show_previous_button 0x7f020033
int attr show_rewind_button 0x7f020034
int attr show_shuffle_button 0x7f020035
int attr show_subtitle_button 0x7f020036
int attr show_timeout 0x7f020037
int attr show_vr_button 0x7f020038
int attr shutter_background_color 0x7f020039
int attr spanCount 0x7f02003a
int attr stackFromEnd 0x7f02003b
int attr surface_type 0x7f02003c
int attr time_bar_min_update_interval 0x7f02003d
int attr touch_target_height 0x7f02003e
int attr ttcIndex 0x7f02003f
int attr unplayed_color 0x7f020040
int attr use_artwork 0x7f020041
int attr use_controller 0x7f020042
int color androidx_core_ripple_material_light 0x7f030000
int color androidx_core_secondary_text_default_material_light 0x7f030001
int color exo_black_opacity_60 0x7f030002
int color exo_black_opacity_70 0x7f030003
int color exo_bottom_bar_background 0x7f030004
int color exo_edit_mode_background_color 0x7f030005
int color exo_error_message_background_color 0x7f030006
int color exo_styled_error_message_background 0x7f030007
int color exo_white 0x7f030008
int color exo_white_opacity_70 0x7f030009
int color notification_action_color_filter 0x7f03000a
int color notification_icon_bg_color 0x7f03000b
int color notification_material_background_media_default_color 0x7f03000c
int color primary_text_default_material_dark 0x7f03000d
int color secondary_text_default_material_dark 0x7f03000e
int color vector_tint_color 0x7f03000f
int color vector_tint_theme_color 0x7f030010
int dimen compat_button_inset_horizontal_material 0x7f040000
int dimen compat_button_inset_vertical_material 0x7f040001
int dimen compat_button_padding_horizontal_material 0x7f040002
int dimen compat_button_padding_vertical_material 0x7f040003
int dimen compat_control_corner_material 0x7f040004
int dimen compat_notification_large_icon_max_height 0x7f040005
int dimen compat_notification_large_icon_max_width 0x7f040006
int dimen exo_error_message_height 0x7f040007
int dimen exo_error_message_margin_bottom 0x7f040008
int dimen exo_error_message_text_padding_horizontal 0x7f040009
int dimen exo_error_message_text_padding_vertical 0x7f04000a
int dimen exo_error_message_text_size 0x7f04000b
int dimen exo_icon_horizontal_margin 0x7f04000c
int dimen exo_icon_padding 0x7f04000d
int dimen exo_icon_padding_bottom 0x7f04000e
int dimen exo_icon_size 0x7f04000f
int dimen exo_icon_text_size 0x7f040010
int dimen exo_media_button_height 0x7f040011
int dimen exo_media_button_width 0x7f040012
int dimen exo_setting_width 0x7f040013
int dimen exo_settings_height 0x7f040014
int dimen exo_settings_icon_size 0x7f040015
int dimen exo_settings_main_text_size 0x7f040016
int dimen exo_settings_offset 0x7f040017
int dimen exo_settings_sub_text_size 0x7f040018
int dimen exo_settings_text_height 0x7f040019
int dimen exo_small_icon_height 0x7f04001a
int dimen exo_small_icon_horizontal_margin 0x7f04001b
int dimen exo_small_icon_padding_horizontal 0x7f04001c
int dimen exo_small_icon_padding_vertical 0x7f04001d
int dimen exo_small_icon_width 0x7f04001e
int dimen exo_styled_bottom_bar_height 0x7f04001f
int dimen exo_styled_bottom_bar_margin_top 0x7f040020
int dimen exo_styled_bottom_bar_time_padding 0x7f040021
int dimen exo_styled_controls_padding 0x7f040022
int dimen exo_styled_minimal_controls_margin_bottom 0x7f040023
int dimen exo_styled_progress_bar_height 0x7f040024
int dimen exo_styled_progress_dragged_thumb_size 0x7f040025
int dimen exo_styled_progress_enabled_thumb_size 0x7f040026
int dimen exo_styled_progress_layout_height 0x7f040027
int dimen exo_styled_progress_margin_bottom 0x7f040028
int dimen exo_styled_progress_touch_target_height 0x7f040029
int dimen fastscroll_default_thickness 0x7f04002a
int dimen fastscroll_margin 0x7f04002b
int dimen fastscroll_minimum_range 0x7f04002c
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f04002d
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f04002e
int dimen item_touch_helper_swipe_escape_velocity 0x7f04002f
int dimen notification_action_icon_size 0x7f040030
int dimen notification_action_text_size 0x7f040031
int dimen notification_big_circle_margin 0x7f040032
int dimen notification_content_margin_start 0x7f040033
int dimen notification_large_icon_height 0x7f040034
int dimen notification_large_icon_width 0x7f040035
int dimen notification_main_column_padding_top 0x7f040036
int dimen notification_media_narrow_margin 0x7f040037
int dimen notification_right_icon_size 0x7f040038
int dimen notification_right_side_padding_top 0x7f040039
int dimen notification_small_icon_background_padding 0x7f04003a
int dimen notification_small_icon_size_as_large 0x7f04003b
int dimen notification_subtext_size 0x7f04003c
int dimen notification_top_pad 0x7f04003d
int dimen notification_top_pad_large_text 0x7f04003e
int drawable exo_edit_mode_logo 0x7f050000
int drawable exo_ic_audiotrack 0x7f050001
int drawable exo_ic_check 0x7f050002
int drawable exo_ic_chevron_left 0x7f050003
int drawable exo_ic_chevron_right 0x7f050004
int drawable exo_ic_default_album_image 0x7f050005
int drawable exo_ic_forward 0x7f050006
int drawable exo_ic_fullscreen_enter 0x7f050007
int drawable exo_ic_fullscreen_exit 0x7f050008
int drawable exo_ic_pause_circle_filled 0x7f050009
int drawable exo_ic_play_circle_filled 0x7f05000a
int drawable exo_ic_rewind 0x7f05000b
int drawable exo_ic_settings 0x7f05000c
int drawable exo_ic_skip_next 0x7f05000d
int drawable exo_ic_skip_previous 0x7f05000e
int drawable exo_ic_speed 0x7f05000f
int drawable exo_ic_subtitle_off 0x7f050010
int drawable exo_ic_subtitle_on 0x7f050011
int drawable exo_icon_circular_play 0x7f050012
int drawable exo_icon_fastforward 0x7f050013
int drawable exo_icon_fullscreen_enter 0x7f050014
int drawable exo_icon_fullscreen_exit 0x7f050015
int drawable exo_icon_next 0x7f050016
int drawable exo_icon_pause 0x7f050017
int drawable exo_icon_play 0x7f050018
int drawable exo_icon_previous 0x7f050019
int drawable exo_icon_repeat_all 0x7f05001a
int drawable exo_icon_repeat_off 0x7f05001b
int drawable exo_icon_repeat_one 0x7f05001c
int drawable exo_icon_rewind 0x7f05001d
int drawable exo_icon_shuffle_off 0x7f05001e
int drawable exo_icon_shuffle_on 0x7f05001f
int drawable exo_icon_stop 0x7f050020
int drawable exo_icon_vr 0x7f050021
int drawable exo_legacy_controls_fastforward 0x7f050022
int drawable exo_legacy_controls_fullscreen_enter 0x7f050023
int drawable exo_legacy_controls_fullscreen_exit 0x7f050024
int drawable exo_legacy_controls_next 0x7f050025
int drawable exo_legacy_controls_pause 0x7f050026
int drawable exo_legacy_controls_play 0x7f050027
int drawable exo_legacy_controls_previous 0x7f050028
int drawable exo_legacy_controls_repeat_all 0x7f050029
int drawable exo_legacy_controls_repeat_off 0x7f05002a
int drawable exo_legacy_controls_repeat_one 0x7f05002b
int drawable exo_legacy_controls_rewind 0x7f05002c
int drawable exo_legacy_controls_shuffle_off 0x7f05002d
int drawable exo_legacy_controls_shuffle_on 0x7f05002e
int drawable exo_legacy_controls_vr 0x7f05002f
int drawable exo_notification_fastforward 0x7f050030
int drawable exo_notification_next 0x7f050031
int drawable exo_notification_pause 0x7f050032
int drawable exo_notification_play 0x7f050033
int drawable exo_notification_previous 0x7f050034
int drawable exo_notification_rewind 0x7f050035
int drawable exo_notification_small_icon 0x7f050036
int drawable exo_notification_stop 0x7f050037
int drawable exo_rounded_rectangle 0x7f050038
int drawable exo_styled_controls_audiotrack 0x7f050039
int drawable exo_styled_controls_check 0x7f05003a
int drawable exo_styled_controls_fastforward 0x7f05003b
int drawable exo_styled_controls_fullscreen_enter 0x7f05003c
int drawable exo_styled_controls_fullscreen_exit 0x7f05003d
int drawable exo_styled_controls_next 0x7f05003e
int drawable exo_styled_controls_overflow_hide 0x7f05003f
int drawable exo_styled_controls_overflow_show 0x7f050040
int drawable exo_styled_controls_pause 0x7f050041
int drawable exo_styled_controls_play 0x7f050042
int drawable exo_styled_controls_previous 0x7f050043
int drawable exo_styled_controls_repeat_all 0x7f050044
int drawable exo_styled_controls_repeat_off 0x7f050045
int drawable exo_styled_controls_repeat_one 0x7f050046
int drawable exo_styled_controls_rewind 0x7f050047
int drawable exo_styled_controls_settings 0x7f050048
int drawable exo_styled_controls_shuffle_off 0x7f050049
int drawable exo_styled_controls_shuffle_on 0x7f05004a
int drawable exo_styled_controls_speed 0x7f05004b
int drawable exo_styled_controls_subtitle_off 0x7f05004c
int drawable exo_styled_controls_subtitle_on 0x7f05004d
int drawable exo_styled_controls_vr 0x7f05004e
int drawable notification_action_background 0x7f05004f
int drawable notification_bg 0x7f050050
int drawable notification_bg_low 0x7f050051
int drawable notification_bg_low_normal 0x7f050052
int drawable notification_bg_low_pressed 0x7f050053
int drawable notification_bg_normal 0x7f050054
int drawable notification_bg_normal_pressed 0x7f050055
int drawable notification_icon_background 0x7f050056
int drawable notification_template_icon_bg 0x7f050057
int drawable notification_template_icon_low_bg 0x7f050058
int drawable notification_tile_bg 0x7f050059
int drawable notify_panel_notification_icon_bg 0x7f05005a
int font roboto_medium_numbers 0x7f060000
int id accessibility_action_clickable_span 0x7f070000
int id accessibility_custom_action_0 0x7f070001
int id accessibility_custom_action_1 0x7f070002
int id accessibility_custom_action_10 0x7f070003
int id accessibility_custom_action_11 0x7f070004
int id accessibility_custom_action_12 0x7f070005
int id accessibility_custom_action_13 0x7f070006
int id accessibility_custom_action_14 0x7f070007
int id accessibility_custom_action_15 0x7f070008
int id accessibility_custom_action_16 0x7f070009
int id accessibility_custom_action_17 0x7f07000a
int id accessibility_custom_action_18 0x7f07000b
int id accessibility_custom_action_19 0x7f07000c
int id accessibility_custom_action_2 0x7f07000d
int id accessibility_custom_action_20 0x7f07000e
int id accessibility_custom_action_21 0x7f07000f
int id accessibility_custom_action_22 0x7f070010
int id accessibility_custom_action_23 0x7f070011
int id accessibility_custom_action_24 0x7f070012
int id accessibility_custom_action_25 0x7f070013
int id accessibility_custom_action_26 0x7f070014
int id accessibility_custom_action_27 0x7f070015
int id accessibility_custom_action_28 0x7f070016
int id accessibility_custom_action_29 0x7f070017
int id accessibility_custom_action_3 0x7f070018
int id accessibility_custom_action_30 0x7f070019
int id accessibility_custom_action_31 0x7f07001a
int id accessibility_custom_action_4 0x7f07001b
int id accessibility_custom_action_5 0x7f07001c
int id accessibility_custom_action_6 0x7f07001d
int id accessibility_custom_action_7 0x7f07001e
int id accessibility_custom_action_8 0x7f07001f
int id accessibility_custom_action_9 0x7f070020
int id action0 0x7f070021
int id action_container 0x7f070022
int id action_divider 0x7f070023
int id action_image 0x7f070024
int id action_text 0x7f070025
int id actions 0x7f070026
int id all 0x7f070027
int id always 0x7f070028
int id androidx_compose_ui_view_composition_context 0x7f070029
int id async 0x7f07002a
int id blocking 0x7f07002b
int id bottom 0x7f07002c
int id cancel_action 0x7f07002d
int id center 0x7f07002e
int id chronometer 0x7f07002f
int id compose_view_saveable_id_tag 0x7f070030
int id consume_window_insets_tag 0x7f070031
int id dialog_button 0x7f070032
int id end_padder 0x7f070033
int id exo_ad_overlay 0x7f070034
int id exo_artwork 0x7f070035
int id exo_audio_track 0x7f070036
int id exo_basic_controls 0x7f070037
int id exo_bottom_bar 0x7f070038
int id exo_buffering 0x7f070039
int id exo_center_controls 0x7f07003a
int id exo_check 0x7f07003b
int id exo_content_frame 0x7f07003c
int id exo_controller 0x7f07003d
int id exo_controller_placeholder 0x7f07003e
int id exo_controls_background 0x7f07003f
int id exo_duration 0x7f070040
int id exo_error_message 0x7f070041
int id exo_extra_controls 0x7f070042
int id exo_extra_controls_scroll_view 0x7f070043
int id exo_ffwd 0x7f070044
int id exo_ffwd_with_amount 0x7f070045
int id exo_fullscreen 0x7f070046
int id exo_icon 0x7f070047
int id exo_main_text 0x7f070048
int id exo_minimal_controls 0x7f070049
int id exo_minimal_fullscreen 0x7f07004a
int id exo_next 0x7f07004b
int id exo_overflow_hide 0x7f07004c
int id exo_overflow_show 0x7f07004d
int id exo_overlay 0x7f07004e
int id exo_pause 0x7f07004f
int id exo_play 0x7f070050
int id exo_play_pause 0x7f070051
int id exo_playback_speed 0x7f070052
int id exo_position 0x7f070053
int id exo_prev 0x7f070054
int id exo_progress 0x7f070055
int id exo_progress_placeholder 0x7f070056
int id exo_repeat_toggle 0x7f070057
int id exo_rew 0x7f070058
int id exo_rew_with_amount 0x7f070059
int id exo_settings 0x7f07005a
int id exo_settings_listview 0x7f07005b
int id exo_shuffle 0x7f07005c
int id exo_shutter 0x7f07005d
int id exo_sub_text 0x7f07005e
int id exo_subtitle 0x7f07005f
int id exo_subtitles 0x7f070060
int id exo_text 0x7f070061
int id exo_time 0x7f070062
int id exo_track_selection_view 0x7f070063
int id exo_vr 0x7f070064
int id fill 0x7f070065
int id fit 0x7f070066
int id fixed_height 0x7f070067
int id fixed_width 0x7f070068
int id forever 0x7f070069
int id hide_in_inspector_tag 0x7f07006a
int id icon 0x7f07006b
int id icon_group 0x7f07006c
int id info 0x7f07006d
int id inspection_slot_table_set 0x7f07006e
int id is_pooling_container_tag 0x7f07006f
int id italic 0x7f070070
int id item_touch_helper_previous_elevation 0x7f070071
int id line1 0x7f070072
int id line3 0x7f070073
int id media_actions 0x7f070074
int id media_controller_compat_view_tag 0x7f070075
int id never 0x7f070076
int id none 0x7f070077
int id normal 0x7f070078
int id notification_background 0x7f070079
int id notification_main_column 0x7f07007a
int id notification_main_column_container 0x7f07007b
int id off 0x7f07007c
int id one 0x7f07007d
int id pooling_container_listener_holder_tag 0x7f07007e
int id report_drawn 0x7f07007f
int id right_icon 0x7f070080
int id right_side 0x7f070081
int id spherical_gl_surface_view 0x7f070082
int id status_bar_latest_event_content 0x7f070083
int id surface_view 0x7f070084
int id tag_accessibility_actions 0x7f070085
int id tag_accessibility_clickable_spans 0x7f070086
int id tag_accessibility_heading 0x7f070087
int id tag_accessibility_pane_title 0x7f070088
int id tag_on_apply_window_listener 0x7f070089
int id tag_on_receive_content_listener 0x7f07008a
int id tag_on_receive_content_mime_types 0x7f07008b
int id tag_screen_reader_focusable 0x7f07008c
int id tag_state_description 0x7f07008d
int id tag_transition_group 0x7f07008e
int id tag_unhandled_key_event_manager 0x7f07008f
int id tag_unhandled_key_listeners 0x7f070090
int id tag_window_insets_animation_callback 0x7f070091
int id text 0x7f070092
int id text2 0x7f070093
int id texture_view 0x7f070094
int id time 0x7f070095
int id title 0x7f070096
int id video_decoder_gl_surface_view 0x7f070097
int id view_tree_lifecycle_owner 0x7f070098
int id view_tree_on_back_pressed_dispatcher_owner 0x7f070099
int id view_tree_saved_state_registry_owner 0x7f07009a
int id view_tree_view_model_store_owner 0x7f07009b
int id when_playing 0x7f07009c
int id wrapped_composition_tag 0x7f07009d
int id zoom 0x7f07009e
int integer cancel_button_image_alpha 0x7f080000
int integer exo_media_button_opacity_percentage_disabled 0x7f080001
int integer exo_media_button_opacity_percentage_enabled 0x7f080002
int integer status_bar_notification_info_maxnum 0x7f080003
int layout custom_dialog 0x7f090000
int layout exo_legacy_player_control_view 0x7f090001
int layout exo_list_divider 0x7f090002
int layout exo_player_control_ffwd_button 0x7f090003
int layout exo_player_control_rewind_button 0x7f090004
int layout exo_player_control_view 0x7f090005
int layout exo_player_view 0x7f090006
int layout exo_styled_settings_list 0x7f090007
int layout exo_styled_settings_list_item 0x7f090008
int layout exo_styled_sub_settings_list_item 0x7f090009
int layout exo_track_selection_dialog 0x7f09000a
int layout notification_action 0x7f09000b
int layout notification_action_tombstone 0x7f09000c
int layout notification_media_action 0x7f09000d
int layout notification_media_cancel_action 0x7f09000e
int layout notification_template_big_media 0x7f09000f
int layout notification_template_big_media_custom 0x7f090010
int layout notification_template_big_media_narrow 0x7f090011
int layout notification_template_big_media_narrow_custom 0x7f090012
int layout notification_template_custom_big 0x7f090013
int layout notification_template_icon_group 0x7f090014
int layout notification_template_lines_media 0x7f090015
int layout notification_template_media 0x7f090016
int layout notification_template_media_custom 0x7f090017
int layout notification_template_part_chronometer 0x7f090018
int layout notification_template_part_time 0x7f090019
int mipmap ic_launcher 0x7f0a0000
int plurals exo_controls_fastforward_by_amount_description 0x7f0b0000
int plurals exo_controls_rewind_by_amount_description 0x7f0b0001
int string androidx_startup 0x7f0c0000
int string app_name 0x7f0c0001
int string close_drawer 0x7f0c0002
int string close_sheet 0x7f0c0003
int string default_error_message 0x7f0c0004
int string default_popup_window_title 0x7f0c0005
int string dropdown_menu 0x7f0c0006
int string exo_controls_cc_disabled_description 0x7f0c0007
int string exo_controls_cc_enabled_description 0x7f0c0008
int string exo_controls_custom_playback_speed 0x7f0c0009
int string exo_controls_fastforward_description 0x7f0c000a
int string exo_controls_fullscreen_enter_description 0x7f0c000b
int string exo_controls_fullscreen_exit_description 0x7f0c000c
int string exo_controls_hide 0x7f0c000d
int string exo_controls_next_description 0x7f0c000e
int string exo_controls_overflow_hide_description 0x7f0c000f
int string exo_controls_overflow_show_description 0x7f0c0010
int string exo_controls_pause_description 0x7f0c0011
int string exo_controls_play_description 0x7f0c0012
int string exo_controls_playback_speed 0x7f0c0013
int string exo_controls_previous_description 0x7f0c0014
int string exo_controls_repeat_all_description 0x7f0c0015
int string exo_controls_repeat_off_description 0x7f0c0016
int string exo_controls_repeat_one_description 0x7f0c0017
int string exo_controls_rewind_description 0x7f0c0018
int string exo_controls_seek_bar_description 0x7f0c0019
int string exo_controls_settings_description 0x7f0c001a
int string exo_controls_show 0x7f0c001b
int string exo_controls_shuffle_off_description 0x7f0c001c
int string exo_controls_shuffle_on_description 0x7f0c001d
int string exo_controls_stop_description 0x7f0c001e
int string exo_controls_time_placeholder 0x7f0c001f
int string exo_controls_vr_description 0x7f0c0020
int string exo_download_completed 0x7f0c0021
int string exo_download_description 0x7f0c0022
int string exo_download_downloading 0x7f0c0023
int string exo_download_failed 0x7f0c0024
int string exo_download_notification_channel_name 0x7f0c0025
int string exo_download_paused 0x7f0c0026
int string exo_download_paused_for_network 0x7f0c0027
int string exo_download_paused_for_wifi 0x7f0c0028
int string exo_download_removing 0x7f0c0029
int string exo_item_list 0x7f0c002a
int string exo_track_bitrate 0x7f0c002b
int string exo_track_mono 0x7f0c002c
int string exo_track_resolution 0x7f0c002d
int string exo_track_role_alternate 0x7f0c002e
int string exo_track_role_closed_captions 0x7f0c002f
int string exo_track_role_commentary 0x7f0c0030
int string exo_track_role_supplementary 0x7f0c0031
int string exo_track_selection_auto 0x7f0c0032
int string exo_track_selection_none 0x7f0c0033
int string exo_track_selection_title_audio 0x7f0c0034
int string exo_track_selection_title_text 0x7f0c0035
int string exo_track_selection_title_video 0x7f0c0036
int string exo_track_stereo 0x7f0c0037
int string exo_track_surround 0x7f0c0038
int string exo_track_surround_5_point_1 0x7f0c0039
int string exo_track_surround_7_point_1 0x7f0c003a
int string exo_track_unknown 0x7f0c003b
int string in_progress 0x7f0c003c
int string indeterminate 0x7f0c003d
int string navigation_menu 0x7f0c003e
int string not_selected 0x7f0c003f
int string off 0x7f0c0040
int string on 0x7f0c0041
int string range_end 0x7f0c0042
int string range_start 0x7f0c0043
int string selected 0x7f0c0044
int string status_bar_notification_info_overflow 0x7f0c0045
int string switch_role 0x7f0c0046
int string tab 0x7f0c0047
int string template_percent 0x7f0c0048
int style DialogWindowTheme 0x7f0d0000
int style ExoMediaButton 0x7f0d0001
int style ExoMediaButton_FastForward 0x7f0d0002
int style ExoMediaButton_Next 0x7f0d0003
int style ExoMediaButton_Pause 0x7f0d0004
int style ExoMediaButton_Play 0x7f0d0005
int style ExoMediaButton_Previous 0x7f0d0006
int style ExoMediaButton_Rewind 0x7f0d0007
int style ExoMediaButton_VR 0x7f0d0008
int style ExoStyledControls 0x7f0d0009
int style ExoStyledControls_Button 0x7f0d000a
int style ExoStyledControls_Button_Bottom 0x7f0d000b
int style ExoStyledControls_Button_Bottom_AudioTrack 0x7f0d000c
int style ExoStyledControls_Button_Bottom_CC 0x7f0d000d
int style ExoStyledControls_Button_Bottom_FullScreen 0x7f0d000e
int style ExoStyledControls_Button_Bottom_OverflowHide 0x7f0d000f
int style ExoStyledControls_Button_Bottom_OverflowShow 0x7f0d0010
int style ExoStyledControls_Button_Bottom_PlaybackSpeed 0x7f0d0011
int style ExoStyledControls_Button_Bottom_RepeatToggle 0x7f0d0012
int style ExoStyledControls_Button_Bottom_Settings 0x7f0d0013
int style ExoStyledControls_Button_Bottom_Shuffle 0x7f0d0014
int style ExoStyledControls_Button_Bottom_VR 0x7f0d0015
int style ExoStyledControls_Button_Center 0x7f0d0016
int style ExoStyledControls_Button_Center_FfwdWithAmount 0x7f0d0017
int style ExoStyledControls_Button_Center_Next 0x7f0d0018
int style ExoStyledControls_Button_Center_PlayPause 0x7f0d0019
int style ExoStyledControls_Button_Center_Previous 0x7f0d001a
int style ExoStyledControls_Button_Center_RewWithAmount 0x7f0d001b
int style ExoStyledControls_TimeBar 0x7f0d001c
int style ExoStyledControls_TimeText 0x7f0d001d
int style ExoStyledControls_TimeText_Duration 0x7f0d001e
int style ExoStyledControls_TimeText_Position 0x7f0d001f
int style ExoStyledControls_TimeText_Separator 0x7f0d0020
int style FloatingDialogTheme 0x7f0d0021
int style FloatingDialogWindowTheme 0x7f0d0022
int style TextAppearance_Compat_Notification 0x7f0d0023
int style TextAppearance_Compat_Notification_Info 0x7f0d0024
int style TextAppearance_Compat_Notification_Info_Media 0x7f0d0025
int style TextAppearance_Compat_Notification_Line2 0x7f0d0026
int style TextAppearance_Compat_Notification_Line2_Media 0x7f0d0027
int style TextAppearance_Compat_Notification_Media 0x7f0d0028
int style TextAppearance_Compat_Notification_Time 0x7f0d0029
int style TextAppearance_Compat_Notification_Time_Media 0x7f0d002a
int style TextAppearance_Compat_Notification_Title 0x7f0d002b
int style TextAppearance_Compat_Notification_Title_Media 0x7f0d002c
int style Theme_MyPlayer 0x7f0d002d
int style Widget_Compat_NotificationActionContainer 0x7f0d002e
int style Widget_Compat_NotificationActionText 0x7f0d002f
int[] styleable AspectRatioFrameLayout { 0x7f020028 }
int styleable AspectRatioFrameLayout_resize_mode 0
int[] styleable Capability { 0x7f020025, 0x7f02002f }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f020002, 0x7f02001f }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable DefaultTimeBar { 0x7f020000, 0x7f020001, 0x7f020007, 0x7f020008, 0x7f020009, 0x7f020022, 0x7f020023, 0x7f02002a, 0x7f02002b, 0x7f02002c, 0x7f02002d, 0x7f02002e, 0x7f02003e, 0x7f020040 }
int styleable DefaultTimeBar_ad_marker_color 0
int styleable DefaultTimeBar_ad_marker_width 1
int styleable DefaultTimeBar_bar_gravity 2
int styleable DefaultTimeBar_bar_height 3
int styleable DefaultTimeBar_buffered_color 4
int styleable DefaultTimeBar_played_ad_marker_color 5
int styleable DefaultTimeBar_played_color 6
int styleable DefaultTimeBar_scrubber_color 7
int styleable DefaultTimeBar_scrubber_disabled_size 8
int styleable DefaultTimeBar_scrubber_dragged_size 9
int styleable DefaultTimeBar_scrubber_drawable 10
int styleable DefaultTimeBar_scrubber_enabled_size 11
int styleable DefaultTimeBar_touch_target_height 12
int styleable DefaultTimeBar_unplayed_color 13
int[] styleable FontFamily { 0x7f020012, 0x7f020013, 0x7f020014, 0x7f020015, 0x7f020016, 0x7f020017, 0x7f020018 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f020011, 0x7f020019, 0x7f02001a, 0x7f02001b, 0x7f02003f }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LegacyPlayerControlView { 0x7f020000, 0x7f020001, 0x7f020007, 0x7f020008, 0x7f020009, 0x7f02000a, 0x7f020022, 0x7f020023, 0x7f020027, 0x7f02002a, 0x7f02002b, 0x7f02002c, 0x7f02002d, 0x7f02002e, 0x7f020031, 0x7f020032, 0x7f020033, 0x7f020034, 0x7f020035, 0x7f020037, 0x7f02003d, 0x7f02003e, 0x7f020040 }
int styleable LegacyPlayerControlView_ad_marker_color 0
int styleable LegacyPlayerControlView_ad_marker_width 1
int styleable LegacyPlayerControlView_bar_gravity 2
int styleable LegacyPlayerControlView_bar_height 3
int styleable LegacyPlayerControlView_buffered_color 4
int styleable LegacyPlayerControlView_controller_layout_id 5
int styleable LegacyPlayerControlView_played_ad_marker_color 6
int styleable LegacyPlayerControlView_played_color 7
int styleable LegacyPlayerControlView_repeat_toggle_modes 8
int styleable LegacyPlayerControlView_scrubber_color 9
int styleable LegacyPlayerControlView_scrubber_disabled_size 10
int styleable LegacyPlayerControlView_scrubber_dragged_size 11
int styleable LegacyPlayerControlView_scrubber_drawable 12
int styleable LegacyPlayerControlView_scrubber_enabled_size 13
int styleable LegacyPlayerControlView_show_fastforward_button 14
int styleable LegacyPlayerControlView_show_next_button 15
int styleable LegacyPlayerControlView_show_previous_button 16
int styleable LegacyPlayerControlView_show_rewind_button 17
int styleable LegacyPlayerControlView_show_shuffle_button 18
int styleable LegacyPlayerControlView_show_timeout 19
int styleable LegacyPlayerControlView_time_bar_min_update_interval 20
int styleable LegacyPlayerControlView_touch_target_height 21
int styleable LegacyPlayerControlView_unplayed_color 22
int[] styleable PlayerControlView { 0x7f020000, 0x7f020001, 0x7f020003, 0x7f020007, 0x7f020008, 0x7f020009, 0x7f02000a, 0x7f020022, 0x7f020023, 0x7f020027, 0x7f02002a, 0x7f02002b, 0x7f02002c, 0x7f02002d, 0x7f02002e, 0x7f020031, 0x7f020032, 0x7f020033, 0x7f020034, 0x7f020035, 0x7f020036, 0x7f020037, 0x7f020038, 0x7f02003d, 0x7f02003e, 0x7f020040 }
int styleable PlayerControlView_ad_marker_color 0
int styleable PlayerControlView_ad_marker_width 1
int styleable PlayerControlView_animation_enabled 2
int styleable PlayerControlView_bar_gravity 3
int styleable PlayerControlView_bar_height 4
int styleable PlayerControlView_buffered_color 5
int styleable PlayerControlView_controller_layout_id 6
int styleable PlayerControlView_played_ad_marker_color 7
int styleable PlayerControlView_played_color 8
int styleable PlayerControlView_repeat_toggle_modes 9
int styleable PlayerControlView_scrubber_color 10
int styleable PlayerControlView_scrubber_disabled_size 11
int styleable PlayerControlView_scrubber_dragged_size 12
int styleable PlayerControlView_scrubber_drawable 13
int styleable PlayerControlView_scrubber_enabled_size 14
int styleable PlayerControlView_show_fastforward_button 15
int styleable PlayerControlView_show_next_button 16
int styleable PlayerControlView_show_previous_button 17
int styleable PlayerControlView_show_rewind_button 18
int styleable PlayerControlView_show_shuffle_button 19
int styleable PlayerControlView_show_subtitle_button 20
int styleable PlayerControlView_show_timeout 21
int styleable PlayerControlView_show_vr_button 22
int styleable PlayerControlView_time_bar_min_update_interval 23
int styleable PlayerControlView_touch_target_height 24
int styleable PlayerControlView_unplayed_color 25
int[] styleable PlayerView { 0x7f020000, 0x7f020001, 0x7f020003, 0x7f020004, 0x7f020005, 0x7f020007, 0x7f020008, 0x7f020009, 0x7f02000a, 0x7f02000b, 0x7f02001c, 0x7f02001d, 0x7f02001e, 0x7f020022, 0x7f020023, 0x7f020024, 0x7f020027, 0x7f020028, 0x7f02002a, 0x7f02002b, 0x7f02002c, 0x7f02002d, 0x7f02002e, 0x7f020030, 0x7f020035, 0x7f020036, 0x7f020037, 0x7f020038, 0x7f020039, 0x7f02003c, 0x7f02003d, 0x7f02003e, 0x7f020040, 0x7f020041, 0x7f020042 }
int styleable PlayerView_ad_marker_color 0
int styleable PlayerView_ad_marker_width 1
int styleable PlayerView_animation_enabled 2
int styleable PlayerView_artwork_display_mode 3
int styleable PlayerView_auto_show 4
int styleable PlayerView_bar_gravity 5
int styleable PlayerView_bar_height 6
int styleable PlayerView_buffered_color 7
int styleable PlayerView_controller_layout_id 8
int styleable PlayerView_default_artwork 9
int styleable PlayerView_hide_during_ads 10
int styleable PlayerView_hide_on_touch 11
int styleable PlayerView_keep_content_on_player_reset 12
int styleable PlayerView_played_ad_marker_color 13
int styleable PlayerView_played_color 14
int styleable PlayerView_player_layout_id 15
int styleable PlayerView_repeat_toggle_modes 16
int styleable PlayerView_resize_mode 17
int styleable PlayerView_scrubber_color 18
int styleable PlayerView_scrubber_disabled_size 19
int styleable PlayerView_scrubber_dragged_size 20
int styleable PlayerView_scrubber_drawable 21
int styleable PlayerView_scrubber_enabled_size 22
int styleable PlayerView_show_buffering 23
int styleable PlayerView_show_shuffle_button 24
int styleable PlayerView_show_subtitle_button 25
int styleable PlayerView_show_timeout 26
int styleable PlayerView_show_vr_button 27
int styleable PlayerView_shutter_background_color 28
int styleable PlayerView_surface_type 29
int styleable PlayerView_time_bar_min_update_interval 30
int styleable PlayerView_touch_target_height 31
int styleable PlayerView_unplayed_color 32
int styleable PlayerView_use_artwork 33
int styleable PlayerView_use_controller 34
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f02000c, 0x7f02000d, 0x7f02000e, 0x7f02000f, 0x7f020010, 0x7f020020, 0x7f020029, 0x7f02003a, 0x7f02003b }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int xml backup_rules 0x7f0f0000
int xml data_extraction_rules 0x7f0f0001
