-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:2:1-38:12
INJECTED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:2:1-38:12
INJECTED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:2:1-38:12
INJECTED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:2:1-38:12
MERGED from [androidx.compose.material:material-ripple:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c59cffeb26ce78ce6765695e8559c22\transformed\material-ripple-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-icons-core:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502cd5e505b88a6e8ad565da22cd9b0d\transformed\material-icons-core-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-icons-extended:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21cf2c758dc2db70416ffaa225c761c1\transformed\material-icons-extended-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0fe038c8f85f2a1cd5dc5b1f834bbc9\transformed\animation-core-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9bb3d2337400ea9e19ba23d55f5db21\transformed\animation-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation-layout:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6673c2bdc5372da129eb07c84fa5a9f\transformed\foundation-layout-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f42ea86275cfb16e9bf42f73f072affa\transformed\foundation-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d72054319ed990be5a26273109eb4f1\transformed\ui-tooling-data-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-unit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\125a08b3d4e18d1c533414ab60772720\transformed\ui-unit-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-geometry:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5227801139870bcee1b61488d7d0a43b\transformed\ui-geometry-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-util:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7ffacbbee9e3d1f1fead63fa5c6ead9\transformed\ui-util-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-text:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00792ef553d3c8f33c015c9cd933aed7\transformed\ui-text-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6840d54369cacef186a2c03418e5cae4\transformed\ui-tooling-preview-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e79f171d8f1e34a402ced4ae50edf86\transformed\ui-graphics-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\743a53c64b3ad5237960f0f60aeb667d\transformed\ui-test-manifest-1.4.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\103dd26deaedd313bf92b1242ad56ed5\transformed\activity-1.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1033b3010226b00a1e4352095c6ac61f\transformed\activity-ktx-1.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff31d879d5b6ec0e44a3f2816820206f\transformed\activity-compose-1.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\443fb41afb655d27c0a0e8d699a9585a\transformed\ui-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef9dc5600468a050226138b1f48dc60e\transformed\ui-tooling-1.4.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d6777e2684faf051dd0557dbe344a01\transformed\material-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23fbae2d2322a104c33b29b623097531\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9b0abddd5103bd96040e99123d7752f\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8059ba36cb3c46a0f24c1411d7cbc4bc\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33406a84c82e6f0a0be2ebee909defa\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad5ce4cfe177ac66043caf8989aacc8\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3d448b679a7165164c0fe235a80f67\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54e7831577f74f46f95854bc6b60b650\transformed\media3-exoplayer-dash-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7bc3fe1df39c40bcb9c2d32529bdabfb\transformed\media3-exoplayer-hls-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95371bf3f47c5f59d54441c97b8b8dd6\transformed\media3-exoplayer-1.1.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.media3:media3-extractor:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f3d361fedebcdc1346ce2e0bc7f7aaf\transformed\media3-extractor-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-container:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dabbadbca4235b8fd188138bff5ca10\transformed\media3-container-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-datasource:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e291dba9c4e20a46d2f8a945794abce1\transformed\media3-datasource-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-decoder:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fb83b835d6051ff4fe2a1ab3e97b38e\transformed\media3-decoder-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-database:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9be1924f47d3adf0e47ac50f7a8e2f72\transformed\media3-database-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-common:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb9594416e8c3af666e8b412404f5e5\transformed\media3-common-1.1.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.media3:media3-ui:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3bcdf5867897ddb0a08993a0c87c689\transformed\media3-ui-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\24e93206e9e626e34f85f58e5f4e9627\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01f066987bee4a8427cf3d60ef0773bb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0032219ff4d5a77527386a0a0ff913ab\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f551cfd33cef484fbd97e55a86c5963e\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7b4a30af06bfad3355b1b104cc5cb93\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5984c620dccc4b25a272c311f26f03ca\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb62e5fae023fdc2953dd5c3d44ce869\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b93ce6ac5ef9ddc2e4965439e469c73d\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93ff2676ba5030c14ed402a7a3942bba\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fbd5111a97cb9e3ca0076ddba677378\transformed\runtime-saveable-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19fedb1b4a6a21209ed8b266fbfc4a03\transformed\runtime-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8c6de3bd61ea0c1252beea6c5a907e6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c233db79eaa7bf5a4ef2cae0ecd30c92\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a369e9b05bfc9675d1a3b87b09fd3458\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb45bc68b8fa623993831a5c829a5d10\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19e654f34b62dcf01d054b53840c45a1\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:5:22-64
application
ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:7:5-36:19
INJECTED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:7:5-36:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\743a53c64b3ad5237960f0f60aeb667d\transformed\ui-test-manifest-1.4.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\743a53c64b3ad5237960f0f60aeb667d\transformed\ui-test-manifest-1.4.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef9dc5600468a050226138b1f48dc60e\transformed\ui-tooling-1.4.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef9dc5600468a050226138b1f48dc60e\transformed\ui-tooling-1.4.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8c6de3bd61ea0c1252beea6c5a907e6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8c6de3bd61ea0c1252beea6c5a907e6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a369e9b05bfc9675d1a3b87b09fd3458\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a369e9b05bfc9675d1a3b87b09fd3458\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:13:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:12:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:10:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:15:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:11:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:8:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:14:9-46
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:9:9-65
activity#com.myplayer.MainActivity
ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:16:9-35:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:18:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:19:13-50
	android:name
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:17:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:20:13-23:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:22:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:22:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:*+data:mimeType:application/x-mpegURL+data:mimeType:video/*+data:pathPattern:.*\\.m3u8+data:scheme:http+data:scheme:https
ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:24:13-34:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:25:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:25:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:26:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:26:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:27:17-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:27:27-75
data
ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:17-47
	android:host
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:30:23-39
	android:scheme
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:28:23-44
	android:pathPattern
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:31:23-54
	android:mimeType
		ADDED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml:32:23-49
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.material:material-ripple:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c59cffeb26ce78ce6765695e8559c22\transformed\material-ripple-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c59cffeb26ce78ce6765695e8559c22\transformed\material-ripple-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502cd5e505b88a6e8ad565da22cd9b0d\transformed\material-icons-core-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502cd5e505b88a6e8ad565da22cd9b0d\transformed\material-icons-core-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-extended:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21cf2c758dc2db70416ffaa225c761c1\transformed\material-icons-extended-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-extended:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21cf2c758dc2db70416ffaa225c761c1\transformed\material-icons-extended-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0fe038c8f85f2a1cd5dc5b1f834bbc9\transformed\animation-core-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0fe038c8f85f2a1cd5dc5b1f834bbc9\transformed\animation-core-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9bb3d2337400ea9e19ba23d55f5db21\transformed\animation-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9bb3d2337400ea9e19ba23d55f5db21\transformed\animation-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-layout:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6673c2bdc5372da129eb07c84fa5a9f\transformed\foundation-layout-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-layout:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6673c2bdc5372da129eb07c84fa5a9f\transformed\foundation-layout-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f42ea86275cfb16e9bf42f73f072affa\transformed\foundation-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f42ea86275cfb16e9bf42f73f072affa\transformed\foundation-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d72054319ed990be5a26273109eb4f1\transformed\ui-tooling-data-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d72054319ed990be5a26273109eb4f1\transformed\ui-tooling-data-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\125a08b3d4e18d1c533414ab60772720\transformed\ui-unit-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\125a08b3d4e18d1c533414ab60772720\transformed\ui-unit-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-geometry:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5227801139870bcee1b61488d7d0a43b\transformed\ui-geometry-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-geometry:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5227801139870bcee1b61488d7d0a43b\transformed\ui-geometry-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7ffacbbee9e3d1f1fead63fa5c6ead9\transformed\ui-util-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7ffacbbee9e3d1f1fead63fa5c6ead9\transformed\ui-util-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00792ef553d3c8f33c015c9cd933aed7\transformed\ui-text-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00792ef553d3c8f33c015c9cd933aed7\transformed\ui-text-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6840d54369cacef186a2c03418e5cae4\transformed\ui-tooling-preview-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6840d54369cacef186a2c03418e5cae4\transformed\ui-tooling-preview-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e79f171d8f1e34a402ced4ae50edf86\transformed\ui-graphics-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e79f171d8f1e34a402ced4ae50edf86\transformed\ui-graphics-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\743a53c64b3ad5237960f0f60aeb667d\transformed\ui-test-manifest-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\743a53c64b3ad5237960f0f60aeb667d\transformed\ui-test-manifest-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\103dd26deaedd313bf92b1242ad56ed5\transformed\activity-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\103dd26deaedd313bf92b1242ad56ed5\transformed\activity-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1033b3010226b00a1e4352095c6ac61f\transformed\activity-ktx-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1033b3010226b00a1e4352095c6ac61f\transformed\activity-ktx-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff31d879d5b6ec0e44a3f2816820206f\transformed\activity-compose-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff31d879d5b6ec0e44a3f2816820206f\transformed\activity-compose-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\443fb41afb655d27c0a0e8d699a9585a\transformed\ui-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\443fb41afb655d27c0a0e8d699a9585a\transformed\ui-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef9dc5600468a050226138b1f48dc60e\transformed\ui-tooling-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef9dc5600468a050226138b1f48dc60e\transformed\ui-tooling-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d6777e2684faf051dd0557dbe344a01\transformed\material-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d6777e2684faf051dd0557dbe344a01\transformed\material-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23fbae2d2322a104c33b29b623097531\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23fbae2d2322a104c33b29b623097531\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9b0abddd5103bd96040e99123d7752f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9b0abddd5103bd96040e99123d7752f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8059ba36cb3c46a0f24c1411d7cbc4bc\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8059ba36cb3c46a0f24c1411d7cbc4bc\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33406a84c82e6f0a0be2ebee909defa\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33406a84c82e6f0a0be2ebee909defa\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad5ce4cfe177ac66043caf8989aacc8\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad5ce4cfe177ac66043caf8989aacc8\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3d448b679a7165164c0fe235a80f67\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3d448b679a7165164c0fe235a80f67\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54e7831577f74f46f95854bc6b60b650\transformed\media3-exoplayer-dash-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-exoplayer-dash:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54e7831577f74f46f95854bc6b60b650\transformed\media3-exoplayer-dash-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-exoplayer-hls:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7bc3fe1df39c40bcb9c2d32529bdabfb\transformed\media3-exoplayer-hls-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-exoplayer-hls:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7bc3fe1df39c40bcb9c2d32529bdabfb\transformed\media3-exoplayer-hls-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95371bf3f47c5f59d54441c97b8b8dd6\transformed\media3-exoplayer-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95371bf3f47c5f59d54441c97b8b8dd6\transformed\media3-exoplayer-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-extractor:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f3d361fedebcdc1346ce2e0bc7f7aaf\transformed\media3-extractor-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-extractor:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f3d361fedebcdc1346ce2e0bc7f7aaf\transformed\media3-extractor-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-container:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dabbadbca4235b8fd188138bff5ca10\transformed\media3-container-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-container:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dabbadbca4235b8fd188138bff5ca10\transformed\media3-container-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-datasource:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e291dba9c4e20a46d2f8a945794abce1\transformed\media3-datasource-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-datasource:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e291dba9c4e20a46d2f8a945794abce1\transformed\media3-datasource-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-decoder:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fb83b835d6051ff4fe2a1ab3e97b38e\transformed\media3-decoder-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-decoder:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fb83b835d6051ff4fe2a1ab3e97b38e\transformed\media3-decoder-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-database:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9be1924f47d3adf0e47ac50f7a8e2f72\transformed\media3-database-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-database:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9be1924f47d3adf0e47ac50f7a8e2f72\transformed\media3-database-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-common:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb9594416e8c3af666e8b412404f5e5\transformed\media3-common-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-common:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb9594416e8c3af666e8b412404f5e5\transformed\media3-common-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-ui:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3bcdf5867897ddb0a08993a0c87c689\transformed\media3-ui-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-ui:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3bcdf5867897ddb0a08993a0c87c689\transformed\media3-ui-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\24e93206e9e626e34f85f58e5f4e9627\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\24e93206e9e626e34f85f58e5f4e9627\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01f066987bee4a8427cf3d60ef0773bb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01f066987bee4a8427cf3d60ef0773bb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0032219ff4d5a77527386a0a0ff913ab\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0032219ff4d5a77527386a0a0ff913ab\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f551cfd33cef484fbd97e55a86c5963e\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f551cfd33cef484fbd97e55a86c5963e\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7b4a30af06bfad3355b1b104cc5cb93\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7b4a30af06bfad3355b1b104cc5cb93\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5984c620dccc4b25a272c311f26f03ca\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5984c620dccc4b25a272c311f26f03ca\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb62e5fae023fdc2953dd5c3d44ce869\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb62e5fae023fdc2953dd5c3d44ce869\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b93ce6ac5ef9ddc2e4965439e469c73d\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b93ce6ac5ef9ddc2e4965439e469c73d\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93ff2676ba5030c14ed402a7a3942bba\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93ff2676ba5030c14ed402a7a3942bba\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fbd5111a97cb9e3ca0076ddba677378\transformed\runtime-saveable-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fbd5111a97cb9e3ca0076ddba677378\transformed\runtime-saveable-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19fedb1b4a6a21209ed8b266fbfc4a03\transformed\runtime-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19fedb1b4a6a21209ed8b266fbfc4a03\transformed\runtime-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8c6de3bd61ea0c1252beea6c5a907e6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8c6de3bd61ea0c1252beea6c5a907e6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c233db79eaa7bf5a4ef2cae0ecd30c92\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c233db79eaa7bf5a4ef2cae0ecd30c92\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a369e9b05bfc9675d1a3b87b09fd3458\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a369e9b05bfc9675d1a3b87b09fd3458\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb45bc68b8fa623993831a5c829a5d10\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb45bc68b8fa623993831a5c829a5d10\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19e654f34b62dcf01d054b53840c45a1\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19e654f34b62dcf01d054b53840c45a1\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\myPlayer\app\src\main\AndroidManifest.xml
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\743a53c64b3ad5237960f0f60aeb667d\transformed\ui-test-manifest-1.4.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\743a53c64b3ad5237960f0f60aeb667d\transformed\ui-test-manifest-1.4.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\743a53c64b3ad5237960f0f60aeb667d\transformed\ui-test-manifest-1.4.0\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef9dc5600468a050226138b1f48dc60e\transformed\ui-tooling-1.4.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef9dc5600468a050226138b1f48dc60e\transformed\ui-tooling-1.4.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef9dc5600468a050226138b1f48dc60e\transformed\ui-tooling-1.4.0\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a369e9b05bfc9675d1a3b87b09fd3458\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a369e9b05bfc9675d1a3b87b09fd3458\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\845f8ee3d8bb1ecc2dfd8d61e3f5cf2c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e09a96962f1c4fcd6d2ae8840331bbe3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95371bf3f47c5f59d54441c97b8b8dd6\transformed\media3-exoplayer-1.1.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-common:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb9594416e8c3af666e8b412404f5e5\transformed\media3-common-1.1.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-common:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb9594416e8c3af666e8b412404f5e5\transformed\media3-common-1.1.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from [androidx.media3:media3-exoplayer:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95371bf3f47c5f59d54441c97b8b8dd6\transformed\media3-exoplayer-1.1.1\AndroidManifest.xml:24:22-76
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.myplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.myplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b9306a62c95bc1ca3eb6c6fcf39fb4\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8e6416d93fe0b9caaae0433ca8d116\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
