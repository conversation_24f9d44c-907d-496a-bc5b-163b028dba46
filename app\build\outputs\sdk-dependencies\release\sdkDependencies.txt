# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.10"
  }
  digests {
    sha256: "\226\"\366|\276A\350\212\322W\301\270\001\200T\335\002\032`D\026$t\247\213\033\354\324O\025\241i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.8.10"
  }
  digests {
    sha256: "\027\341\aa1\315\a\311X\251B\377\212\b|\370e\261\357=\345\204c\341\345\335o\327QT\006\260"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.8.10"
  }
  digests {
    sha256: "8P1\336Lm\331\351\220\326\244DU\327\225\203W*I\n4d\327\376\177\020\245\354\3122%\250"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.10"
  }
  digests {
    sha256: "\250C\252\340\267#$H\257\306\257\005t|\324\336Ak\234+v\341\000\241\260{\037x\2037\262\355"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.9.0"
  }
  digests {
    sha256: "\025B\241\337{\351\b\311_\356\221\270\333\300?\331t\365?\021\330J\205\330\201\371ZRU\034@Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.5.0"
  }
  digests {
    sha256: "&\037\267\300!\bXP\v\253f\323CT\227*u\026j\264\030*\335(7\200\260U\023\326\354J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.9.0"
  }
  digests {
    sha256: "\213\332>\343\250\210\207\325Ofy\373kl\327\210b\237s#J\311\034\213\276\331$\347!\354\205\270"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.3.0"
  }
  digests {
    sha256: "\253\375)\310Un[\3202Z\237v\232\271\351\321T\377JU\025\304v\315\325\242\250([\033\031\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.6.4"
  }
  digests {
    sha256: "?\334\016\355[\304\270>\351b\'tR\n-\262Tp7\016\254\321X\034\254\0367pO\t[\000"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.6.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.6.4"
  }
  digests {
    sha256: "\302L\213\262{\263 \304\2518qP\032~^\fa`v8\220{\031z\357gU\023\324\310 \276"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.6.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.6.1"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "(\240\2704\364\016\335R\342\370\001\v\3340W\322\240\314v\252\245\254\223\021\255\260\271\316\221\234\251\314"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose"
    version: "2.6.1"
  }
  digests {
    sha256: "\302\270  L,\245\216\371\313\001Z>_\373\350\215\214U\260\236\\\270Q\326+DU(\243y\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.4.0"
  }
  digests {
    sha256: "\vng*\026\2723ia\327&v(\036I\225Z\222\375\240qrZ\336\372\354\252\251B}V~"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.4.0"
  }
  digests {
    sha256: "\267\337O\v\246\340\244\337\030\305\330\300\323\276\020\324Q\023\343G\320\030`7d\320\361\222\273\267\f\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.4.0"
  }
  digests {
    sha256: "g\255\020\316Y\360\226l\370\341\024lQ\030KL&\217\002$\334E\031?\302\005\326\334\327>\350e"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.7.0"
  }
  digests {
    sha256: "\374\343\027\326\032\"\361)g\264u\277\313\200\310\235\332f\344\030\227^\211\016\247\003\313t\341+[\021"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.7.0"
  }
  digests {
    sha256: "\344K 2\'3\207\025i\202\221,Y\036\367\342\335H[\240\262\346\211\265(\265\244/\'\032O\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.1"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\177\300\372#J3!\261\363M\265\206+\027\332\203\321\306,\033\306n\302\375O\033\260\247q\254\372\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.1"
  }
  digests {
    sha256: "\310/\211\"\032\333\341\235\367\307\255\272\266?N\314\205\177\307F\343\311IBV\253\217\245\302\004\221\262"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.1"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.1"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.0"
  }
  digests {
    sha256: "4\350\262\277\307N#\301R^=\251\003\256D\233\177\033D\n\357E\341\201Y\356G\016\221\231\177H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.7.0"
  }
  digests {
    sha256: "\312\247(\205\321\316yy\301\326\305\232\213%\\`\227\267px\rMM\251]V\227\2324\206F\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.4.0"
  }
  digests {
    sha256: "\301\200t|\260>\363\375,\337\225\204\246I\246\277\231&PN\3462/\'ZK\037\376}\334\312\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.4.0"
  }
  digests {
    sha256: "\177\327\r\242\202F#Ie\016\002\023R\027/e@~\276IME\206:.maIo?\342\276"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.4.0"
  }
  digests {
    sha256: "\340\233v\322\201r\255\251OB\312\317\305\367s}|\312(\000\377Z\\\341\221\334\360\302PF\bf"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.4.0"
  }
  digests {
    sha256: "\224\223\320\245\221K\365q\033;,\315\352\227\346\204\303k\004\273\263{\266;\a\311\215W\315\313\'\253"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.4.0"
  }
  digests {
    sha256: "\\{\260Y8\377\004\262Rd\211\315\273\333g7\366\032p\037\207-i\330\376\332\3169\230\033s\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.4.0"
  }
  digests {
    sha256: "\211\367}\223\217\\cf\001`,\246&t\266\366b\332\372\307\263\220*b\33120\204\227\a\a\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.4.0"
  }
  digests {
    sha256: "`\250\324,\000\224\227\227\a\302\2232U\2176\246&p\266\245H\021\241\356\316\223\343\025\254\333s\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.4.0"
  }
  digests {
    sha256: "rBw\212\325\336\001\327\263w\212\212\311>\264\242}Y\000\374\f\335T\a\254a$\271\314\210\304 "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.4.0"
  }
  digests {
    sha256: "\362u\363\"\271+P_\006k\a\274\337\314^8\321\f\345<\313\232t\211!\221\3030`\3312\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.4.0"
  }
  digests {
    sha256: "Lv \3058\0177\315\322p\224\227#\323\0227\230\371t\001\335\026\203^\300*\323\2340\221Rw"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2023.03.00"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material"
    version: "1.4.0"
  }
  digests {
    sha256: "\203\323\033*\344\263}d`\336\023;\244\255\231\252\252\233p\036\253\331\nlS\253\354\245M\254\201\217"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.4.0"
  }
  digests {
    sha256: "z%F\211\330\n;?\030\335?\021\333\262\017)\210y\fk\306\211 2ZS\234\327\327{\274\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-extended"
    version: "1.4.0"
  }
  digests {
    sha256: "\364\232\tB\270\340:*\023o\005\024\373\370\016\212\323\n\naT\345$\2359\276\310\321\325\371\264\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.4.0"
  }
  digests {
    sha256: "\a\034=\313\326\216\243R\352\344Q\214$W|D\276\316\326\350\300\365\n\333\211H\277\207\366@\306\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer"
    version: "1.1.1"
  }
  digests {
    sha256: "A*j\335\017\033\264\336\365\234\371\\\255\332\242\370\202\250\375\357\360\257F\377\320\353\362x\350\242\2362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-common"
    version: "1.1.1"
  }
  digests {
    sha256: "\"\257D\216\267\257\034\217\303\r\267\220\376\227}\300\177p\022?M\346\017^\341\335\353|@\227_\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-container"
    version: "1.1.1"
  }
  digests {
    sha256: "b\r\313\266yB\364\f\266M\251!\334t;\247]\253\254\350\177~\a?G\a\022\213G-\343\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-database"
    version: "1.1.1"
  }
  digests {
    sha256: "7\020XZ\230CH\336\021!\217cc\003\216\352o#\177\243z\021\321F\b#\244\200Q\246\260P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-datasource"
    version: "1.1.1"
  }
  digests {
    sha256: "\006\334\264\216,&\362\022\273\016u\246x-\232\353\006j\255\256\373\241\f\247\262\315m\331c5\373\353"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-decoder"
    version: "1.1.1"
  }
  digests {
    sha256: "\210\277\323Oa\262\325\250!\324&k\021\314\357\256C\033\024\217\310\214\036\200\005}\335aJ<,\221"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-dash"
    version: "1.1.1"
  }
  digests {
    sha256: "\315loug\236\314HX\252\267\226~\376\177n\311\231>\312\250\256z\006\352\355\370+\220\316\236\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-hls"
    version: "1.1.1"
  }
  digests {
    sha256: "K\222(\260b\036\354\207\266\371\315!\244\340V=\003\tG\215/\346\026i]\212\020\'t\2538\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-extractor"
    version: "1.1.1"
  }
  digests {
    sha256: "#l5\215\344\3660\322>|\375\016\033\357s\016\260\232\225Cf\307\005,\342\257(\314SRh\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-ui"
    version: "1.1.1"
  }
  digests {
    sha256: "\346\264O\323~\a\247g\317\264\250\262\340l\244\243\2156\232\311a\237\210H\257t\263Hy\301\3315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.6.0"
  }
  digests {
    sha256: "\376b\210G\330\371\t\312\217#\326\205\006\037\232v\204\252\f\233\311\301\262\001\304\377n\267\2637(("
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.0"
  }
  digests {
    sha256: "\326Y(\240\017cX\232I\342\031%A.\017H\205/\211%K\a\260<\003\rV\017\221\357\374\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 4
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 4
  library_dep_index: 1
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 1
  library_dep_index: 7
}
library_dependencies {
  library_index: 6
  library_dep_index: 1
}
library_dependencies {
  library_index: 7
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 52
  library_dep_index: 5
}
library_dependencies {
  library_index: 8
  library_dep_index: 1
}
library_dependencies {
  library_index: 9
  library_dep_index: 6
}
library_dependencies {
  library_index: 10
  library_dep_index: 6
  library_dep_index: 11
}
library_dependencies {
  library_index: 12
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 37
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 22
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
}
library_dependencies {
  library_index: 14
  library_dep_index: 6
  library_dep_index: 13
}
library_dependencies {
  library_index: 15
  library_dep_index: 6
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 31
  library_dep_index: 12
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 22
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 32
}
library_dependencies {
  library_index: 16
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 0
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 19
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 17
}
library_dependencies {
  library_index: 20
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 15
  library_dep_index: 12
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 22
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 21
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 12
  library_dep_index: 22
  library_dep_index: 28
  library_dep_index: 20
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 15
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 22
  library_dep_index: 8
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 20
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 1
  library_dep_index: 20
  library_dep_index: 12
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 15
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 23
  library_dep_index: 6
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 6
  library_dep_index: 23
  library_dep_index: 1
  library_dep_index: 23
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
  library_dep_index: 6
  library_dep_index: 39
  library_dep_index: 9
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 43
  library_dep_index: 41
  library_dep_index: 7
  library_dep_index: 47
  library_dep_index: 45
  library_dep_index: 12
  library_dep_index: 28
  library_dep_index: 37
  library_dep_index: 36
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 43
  library_dep_index: 41
  library_dep_index: 48
}
library_dependencies {
  library_index: 26
  library_dep_index: 27
  library_dep_index: 5
  library_dep_index: 21
  library_dep_index: 29
  library_dep_index: 36
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 38
}
library_dependencies {
  library_index: 27
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 12
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 37
  library_dep_index: 35
  library_dep_index: 34
  library_dep_index: 1
  library_dep_index: 38
  library_dep_index: 26
}
library_dependencies {
  library_index: 28
  library_dep_index: 6
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 20
  library_dep_index: 12
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 29
  library_dep_index: 28
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 15
  library_dep_index: 20
  library_dep_index: 12
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 22
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 30
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 31
  library_dep_index: 28
  library_dep_index: 35
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 15
  library_dep_index: 20
  library_dep_index: 31
  library_dep_index: 12
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 22
  library_dep_index: 29
  library_dep_index: 32
}
library_dependencies {
  library_index: 31
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 20
  library_dep_index: 12
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 22
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 33
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 20
  library_dep_index: 31
  library_dep_index: 12
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 22
  library_dep_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 33
  library_dep_index: 6
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 6
}
library_dependencies {
  library_index: 35
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 1
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 35
  library_dep_index: 1
  library_dep_index: 35
}
library_dependencies {
  library_index: 37
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 33
  library_dep_index: 11
}
library_dependencies {
  library_index: 38
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 28
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 39
  library_dep_index: 7
}
library_dependencies {
  library_index: 40
  library_dep_index: 6
  library_dep_index: 23
  library_dep_index: 41
  library_dep_index: 1
  library_dep_index: 25
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 43
  library_dep_index: 41
}
library_dependencies {
  library_index: 41
  library_dep_index: 1
  library_dep_index: 25
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 43
}
library_dependencies {
  library_index: 42
  library_dep_index: 6
  library_dep_index: 23
  library_dep_index: 43
  library_dep_index: 41
  library_dep_index: 2
  library_dep_index: 25
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 43
  library_dep_index: 41
}
library_dependencies {
  library_index: 43
  library_dep_index: 6
  library_dep_index: 23
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 1
  library_dep_index: 25
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 41
}
library_dependencies {
  library_index: 44
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 41
  library_dep_index: 7
  library_dep_index: 45
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 17
  library_dep_index: 25
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 46
  library_dep_index: 43
  library_dep_index: 41
}
library_dependencies {
  library_index: 45
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 46
  library_dep_index: 6
  library_dep_index: 23
  library_dep_index: 1
  library_dep_index: 25
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 43
  library_dep_index: 41
}
library_dependencies {
  library_index: 47
  library_dep_index: 5
  library_dep_index: 1
}
library_dependencies {
  library_index: 48
  library_dep_index: 6
  library_dep_index: 49
  library_dep_index: 51
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 2
  library_dep_index: 51
}
library_dependencies {
  library_index: 49
  library_dep_index: 6
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 2
  library_dep_index: 50
}
library_dependencies {
  library_index: 50
  library_dep_index: 6
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 43
  library_dep_index: 41
  library_dep_index: 1
  library_dep_index: 17
  library_dep_index: 49
}
library_dependencies {
  library_index: 51
  library_dep_index: 6
  library_dep_index: 50
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 43
  library_dep_index: 41
  library_dep_index: 7
  library_dep_index: 2
  library_dep_index: 48
}
library_dependencies {
  library_index: 52
  library_dep_index: 6
  library_dep_index: 9
}
library_dependencies {
  library_index: 53
  library_dep_index: 54
  library_dep_index: 56
  library_dep_index: 25
  library_dep_index: 42
  library_dep_index: 46
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 48
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 57
  library_dep_index: 44
  library_dep_index: 41
  library_dep_index: 40
  library_dep_index: 43
}
library_dependencies {
  library_index: 54
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 48
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 57
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 44
  library_dep_index: 41
  library_dep_index: 12
  library_dep_index: 28
  library_dep_index: 35
  library_dep_index: 2
  library_dep_index: 55
  library_dep_index: 56
  library_dep_index: 57
}
library_dependencies {
  library_index: 55
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 1
  library_dep_index: 54
  library_dep_index: 56
  library_dep_index: 57
}
library_dependencies {
  library_index: 56
  library_dep_index: 55
  library_dep_index: 23
  library_dep_index: 2
  library_dep_index: 54
  library_dep_index: 55
  library_dep_index: 57
}
library_dependencies {
  library_index: 57
  library_dep_index: 49
  library_dep_index: 48
  library_dep_index: 23
  library_dep_index: 41
  library_dep_index: 2
  library_dep_index: 54
  library_dep_index: 55
  library_dep_index: 56
}
library_dependencies {
  library_index: 58
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 59
  library_dep_index: 62
  library_dep_index: 64
  library_dep_index: 65
  library_dep_index: 68
  library_dep_index: 63
}
library_dependencies {
  library_index: 59
  library_dep_index: 6
  library_dep_index: 60
  library_dep_index: 8
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 64
  library_dep_index: 65
  library_dep_index: 58
  library_dep_index: 66
  library_dep_index: 67
  library_dep_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
  library_dep_index: 11
}
library_dependencies {
  library_index: 62
  library_dep_index: 59
  library_dep_index: 6
}
library_dependencies {
  library_index: 63
  library_dep_index: 59
  library_dep_index: 6
}
library_dependencies {
  library_index: 64
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 6
}
library_dependencies {
  library_index: 65
  library_dep_index: 59
  library_dep_index: 6
}
library_dependencies {
  library_index: 66
  library_dep_index: 58
  library_dep_index: 6
}
library_dependencies {
  library_index: 67
  library_dep_index: 6
  library_dep_index: 58
}
library_dependencies {
  library_index: 68
  library_dep_index: 6
  library_dep_index: 59
  library_dep_index: 62
  library_dep_index: 65
}
library_dependencies {
  library_index: 69
  library_dep_index: 59
  library_dep_index: 70
  library_dep_index: 6
  library_dep_index: 71
}
library_dependencies {
  library_index: 70
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 7
}
library_dependencies {
  library_index: 71
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 72
  library_dep_index: 47
}
library_dependencies {
  library_index: 72
  library_dep_index: 6
  library_dep_index: 7
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 21
  dependency_index: 38
  dependency_index: 53
  dependency_index: 25
  dependency_index: 42
  dependency_index: 46
  dependency_index: 54
  dependency_index: 56
  dependency_index: 58
  dependency_index: 69
  dependency_index: 66
  dependency_index: 67
  dependency_index: 73
  dependency_index: 22
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
