package com.myplayer

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

data class Preset(
    val name: String,
    val url: String,
    val userAgent: String,
    val referer: String?
)

class PresetManager(context: Context) {

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences("player_presets", Context.MODE_PRIVATE)
    private val gson = Gson()

    fun savePreset(preset: Preset) {
        val presets = getPresets().toMutableList()
        // Remove existing preset with the same name if it exists
        presets.removeAll { it.name == preset.name }
        presets.add(preset)
        val json = gson.toJson(presets)
        sharedPreferences.edit().putString("presets", json).apply()
    }

    fun getPresets(): List<Preset> {
        val json = sharedPreferences.getString("presets", null)
        return if (json != null) {
            val type = object : TypeToken<List<Preset>>() {}.type
            gson.fromJson(json, type)
        } else {
            emptyList()
        }
    }

    fun deletePreset(preset: Preset) {
        val presets = getPresets().toMutableList()
        presets.removeAll { it.name == preset.name }
        val json = gson.toJson(presets)
        sharedPreferences.edit().putString("presets", json).apply()
    }
}
